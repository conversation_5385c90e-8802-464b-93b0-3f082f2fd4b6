#!/usr/bin/env python3
"""
安装验证脚本

验证Python视频监控通道管理器的安装和基本功能。
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试工具模块
        from video_monitor.utils.crypto import CryptoUtils, md5, calculate_signature
        print("  ✅ 加密工具模块导入成功")
        
        from video_monitor.utils.ssl_helper import SSLHelper
        print("  ✅ SSL助手模块导入成功")
        
        # 测试配置模块
        from video_monitor.config.settings import Settings
        print("  ✅ 配置管理模块导入成功")
        
        # 测试数据模型
        from video_monitor.models.data_models import ChannelInfo, MonitorUrlInfo, OrganizationInfo
        print("  ✅ 数据模型模块导入成功")
        
        # 测试API模块
        from video_monitor.api.client import ApiClient
        from video_monitor.api.endpoints import ApiEndpoints
        print("  ✅ API客户端模块导入成功")
        
        # 测试认证模块
        from video_monitor.auth.login import LoginManager
        from video_monitor.auth.session import SessionManager
        print("  ✅ 认证模块导入成功")
        
        # 测试业务模块
        from video_monitor.business.monitor_service import MonitorService
        print("  ✅ 业务逻辑模块导入成功")
        
        # 测试主模块
        from video_monitor.main import RealMonitorChannelManager
        from video_monitor import RealMonitorChannelManager as MainClass
        print("  ✅ 主程序模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"  ❌ 导入过程中发生异常: {e}")
        return False


def test_crypto_functions():
    """测试加密功能"""
    print("\n🔐 测试加密功能...")
    
    try:
        from video_monitor.utils.crypto import CryptoUtils
        
        # 测试MD5计算
        test_string = "hello_world"
        md5_result = CryptoUtils.md5(test_string)
        expected_md5 = "5e2bf57d3f40c4b6df49aadd81f961e3"
        
        if md5_result == expected_md5:
            print("  ✅ MD5计算功能正常")
        else:
            print(f"  ❌ MD5计算结果不正确: 期望 {expected_md5}, 实际 {md5_result}")
            return False
        
        # 测试签名计算
        signature = CryptoUtils.calculate_signature(
            password="test123",
            realm="test_realm",
            random_key="random123",
            user_name="testuser"
        )
        
        if len(signature) == 32 and all(c in '0123456789abcdef' for c in signature):
            print("  ✅ 签名计算功能正常")
        else:
            print(f"  ❌ 签名计算结果格式不正确: {signature}")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 加密功能测试失败: {e}")
        return False


def test_data_models():
    """测试数据模型"""
    print("\n📊 测试数据模型...")
    
    try:
        from video_monitor.models.data_models import ChannelInfo, MonitorUrlInfo, OrganizationInfo
        
        # 测试ChannelInfo
        channel = ChannelInfo(
            channel_id="test_001",
            channel_name="测试通道",
            org_name="测试组织",
            org_id="org_001",
            org_level=1
        )
        
        if channel.channel_id == "test_001" and channel.channel_name == "测试通道":
            print("  ✅ ChannelInfo模型正常")
        else:
            print("  ❌ ChannelInfo模型异常")
            return False
        
        # 测试MonitorUrlInfo
        monitor_url = MonitorUrlInfo(
            channel_id="test_001",
            channel_name="测试通道",
            org_name="测试组织",
            scheme="RTSP",
            url="rtsp://test.example.com/stream",
            sub_type=0
        )
        
        if monitor_url.scheme == "RTSP" and monitor_url.sub_type == 0:
            print("  ✅ MonitorUrlInfo模型正常")
        else:
            print("  ❌ MonitorUrlInfo模型异常")
            return False
        
        # 测试OrganizationInfo
        org = OrganizationInfo(
            org_id="org_001",
            org_name="测试组织",
            level=0
        )
        
        # 添加子组织
        child_org = OrganizationInfo(
            org_id="org_002",
            org_name="子组织",
            level=1
        )
        org.add_child(child_org)
        
        # 添加通道
        org.add_channel(channel)
        
        if len(org.children) == 1 and len(org.channels) == 1:
            print("  ✅ OrganizationInfo模型正常")
        else:
            print("  ❌ OrganizationInfo模型异常")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据模型测试失败: {e}")
        return False


def test_configuration():
    """测试配置管理"""
    print("\n⚙️ 测试配置管理...")
    
    try:
        from video_monitor.config.settings import Settings
        
        # 检查资源目录
        resources_dir = Path(__file__).parent / "resources"
        if not resources_dir.exists():
            print("  ⚠️ resources目录不存在，跳过配置测试")
            return True
        
        # 检查配置文件
        baseinfo_file = resources_dir / "baseinfo.properties"
        if not baseinfo_file.exists():
            print("  ⚠️ baseinfo.properties文件不存在，跳过配置测试")
            return True
        
        # 测试配置读取
        settings = Settings(str(resources_dir))
        
        # 尝试读取IP配置
        try:
            ip = settings.get_string('baseinfo', 'ip')
            print(f"  ✅ 配置读取正常，IP: {ip}")
        except Exception:
            print("  ⚠️ 无法读取IP配置，但配置模块功能正常")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 配置管理测试失败: {e}")
        return False


def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    required_packages = [
        'requests',
        'urllib3',
        'certifi'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ 缺失的依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def main():
    """主函数"""
    print("🎯 Python视频监控通道管理器 - 安装验证")
    print("=" * 50)
    
    all_tests_passed = True
    
    # 检查依赖包
    if not check_dependencies():
        all_tests_passed = False
    
    # 测试模块导入
    if not test_imports():
        all_tests_passed = False
    
    # 测试加密功能
    if not test_crypto_functions():
        all_tests_passed = False
    
    # 测试数据模型
    if not test_data_models():
        all_tests_passed = False
    
    # 测试配置管理
    if not test_configuration():
        all_tests_passed = False
    
    print("\n" + "=" * 50)
    
    if all_tests_passed:
        print("🎉 所有测试通过！安装验证成功！")
        print("\n📖 接下来你可以:")
        print("  1. 配置 resources/baseinfo.properties 文件")
        print("  2. 放置SSL证书文件到 resources/ 目录")
        print("  3. 运行示例程序: python examples/basic_usage.py")
        print("  4. 或者直接使用: python -m video_monitor.main")
        return 0
    else:
        print("❌ 部分测试失败，请检查安装")
        return 1


if __name__ == "__main__":
    sys.exit(main())
