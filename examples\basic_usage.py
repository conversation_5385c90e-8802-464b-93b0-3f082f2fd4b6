#!/usr/bin/env python3
"""
基本使用示例

演示如何使用视频监控通道管理器的基本功能。
"""

import sys
import logging
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from video_monitor import RealMonitorChannelManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def basic_example():
    """基本使用示例"""
    print("🚀 视频监控通道管理器 - 基本使用示例")
    print("=" * 50)
    
    # 创建通道管理器（使用上下文管理器确保资源清理）
    with RealMonitorChannelManager() as manager:
        # 1. 执行登录
        print("\n🔐 正在登录...")
        if not manager.perform_secure_login():
            print("❌ 登录失败，请检查配置")
            return False
        
        print("✅ 登录成功")
        
        # 2. 获取系统状态
        status = manager.get_status()
        print(f"\n📊 系统状态:")
        print(f"  - 登录状态: {'✅' if status['logged_in'] else '❌'}")
        print(f"  - Token状态: {'✅' if status['has_token'] else '❌'}")
        print(f"  - 会话状态: {'✅' if status['session_info']['active'] else '❌'}")
        
        # 3. 获取所有通道信息
        print("\n📡 正在获取通道信息...")
        channels = manager.get_sub()
        
        if not channels:
            print("⚠️ 没有找到任何通道")
            return True
        
        print(f"✅ 成功获取 {len(channels)} 个通道")
        
        # 4. 显示通道列表（前10个）
        print(f"\n📋 通道列表 (显示前10个):")
        for i, channel in enumerate(channels[:10], 1):
            print(f"  {i:2d}. [{channel.channel_id}] {channel.channel_name}")
            print(f"      组织: {channel.org_name} (级别: {channel.org_level})")
        
        if len(channels) > 10:
            print(f"      ... 还有 {len(channels) - 10} 个通道")
        
        # 5. 获取第一个通道的监控URI
        if channels:
            first_channel = channels[0]
            print(f"\n🎥 获取通道 '{first_channel.channel_name}' 的监控URI...")
            
            # 获取RTSP主码流
            monitor_url = manager.get_real_monitor_url(
                channel_id=first_channel.channel_id,
                scheme="RTSP",
                sub_type=0
            )
            
            if monitor_url:
                print(f"✅ RTSP监控URI: {monitor_url.url}")
                print(f"   协议: {monitor_url.scheme}")
                print(f"   码流类型: {monitor_url.sub_type} (0:主码流)")
            else:
                print("❌ 获取监控URI失败")
        
        print("\n✅ 示例执行完成")
        return True


def advanced_example():
    """高级使用示例"""
    print("\n🚀 视频监控通道管理器 - 高级使用示例")
    print("=" * 50)
    
    manager = RealMonitorChannelManager()
    
    try:
        # 登录
        print("\n🔐 正在登录...")
        if not manager.perform_secure_login():
            print("❌ 登录失败")
            return False
        
        print("✅ 登录成功")
        
        # 获取通道
        print("\n📡 正在获取通道信息...")
        channels = manager.get_sub()
        
        if not channels:
            print("⚠️ 没有找到任何通道")
            return True
        
        print(f"✅ 成功获取 {len(channels)} 个通道")
        
        # 获取多种协议的监控URI（只处理前3个通道以节省时间）
        print(f"\n🎬 获取多协议监控URI (处理前3个通道)...")
        
        schemes = ["RTSP", "FLV_HTTP", "HLS"]
        sub_types = [0, 1]  # 主码流和辅流1
        
        results = {}
        for scheme in schemes:
            results[scheme] = []
        
        for i, channel in enumerate(channels[:3], 1):
            print(f"\n处理通道 {i}/3: {channel.channel_name}")
            
            for scheme in schemes:
                for sub_type in sub_types:
                    monitor_url = manager.get_real_monitor_url(
                        channel_id=channel.channel_id,
                        scheme=scheme,
                        sub_type=sub_type
                    )
                    
                    if monitor_url:
                        # 更新通道信息
                        monitor_url.channel_name = channel.channel_name
                        monitor_url.org_name = channel.org_name
                        results[scheme].append(monitor_url)
                        
                        stream_type = "主码流" if sub_type == 0 else f"辅流{sub_type}"
                        print(f"  ✅ {scheme} {stream_type}: 获取成功")
                    else:
                        stream_type = "主码流" if sub_type == 0 else f"辅流{sub_type}"
                        print(f"  ❌ {scheme} {stream_type}: 获取失败")
        
        # 显示结果统计
        print(f"\n📊 结果统计:")
        for scheme in schemes:
            count = len(results[scheme])
            print(f"  {scheme}: {count} 个URI")
        
        # 显示部分结果
        print(f"\n🎥 部分监控URI示例:")
        for scheme in schemes:
            if results[scheme]:
                url_info = results[scheme][0]
                print(f"  {scheme}: {url_info.url}")
        
        print("\n✅ 高级示例执行完成")
        return True
        
    finally:
        # 确保登出
        print("\n🚪 正在登出...")
        manager.logout()
        print("✅ 登出完成")


def error_handling_example():
    """错误处理示例"""
    print("\n🚀 视频监控通道管理器 - 错误处理示例")
    print("=" * 50)
    
    try:
        # 尝试使用错误的配置
        print("\n🔧 测试错误处理...")
        
        manager = RealMonitorChannelManager()
        
        # 检查登录状态
        status = manager.get_status()
        print(f"初始状态: {status}")
        
        # 尝试在未登录时获取通道
        print("\n❌ 测试未登录时获取通道...")
        channels = manager.get_sub()
        print(f"未登录时获取到的通道数: {len(channels)}")
        
        # 尝试在未登录时获取监控URI
        print("\n❌ 测试未登录时获取监控URI...")
        monitor_url = manager.get_real_monitor_url("test_channel", "RTSP", 0)
        print(f"未登录时获取监控URI结果: {monitor_url}")
        
        print("\n✅ 错误处理测试完成")
        
    except Exception as e:
        print(f"❌ 发生异常: {e}")
        logger.exception("错误处理示例中发生异常")


def main():
    """主函数"""
    print("🎯 视频监控通道管理器 - 使用示例集合")
    print("=" * 60)
    
    try:
        # 运行基本示例
        if not basic_example():
            print("\n❌ 基本示例执行失败")
            return 1
        
        # 运行高级示例
        if not advanced_example():
            print("\n❌ 高级示例执行失败")
            return 1
        
        # 运行错误处理示例
        error_handling_example()
        
        print("\n🎉 所有示例执行完成！")
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
        return 0
    except Exception as e:
        print(f"\n❌ 程序执行异常: {e}")
        logger.exception("主程序执行异常")
        return 1


if __name__ == "__main__":
    sys.exit(main())
