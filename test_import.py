#!/usr/bin/env python3
"""
测试导入脚本，用于诊断导入问题
"""

import sys
import traceback

def test_import(module_name, description):
    """测试导入模块"""
    print(f"Testing {description}...")
    try:
        if module_name == "video_monitor.config.settings":
            from video_monitor.config.settings import Settings
            print(f"✅ {description} imported successfully")
            return True
        elif module_name == "video_monitor.utils.ssl_helper":
            from video_monitor.utils.ssl_helper import SSLHelper
            print(f"✅ {description} imported successfully")
            return True
        elif module_name == "video_monitor.api.client":
            from video_monitor.api.client import ApiClient
            print(f"✅ {description} imported successfully")
            return True
        elif module_name == "video_monitor.main":
            from video_monitor.main import RealMonitorChannelManager
            print(f"✅ {description} imported successfully")
            return True
        else:
            exec(f"import {module_name}")
            print(f"✅ {description} imported successfully")
            return True
    except Exception as e:
        print(f"❌ {description} failed: {e}")
        traceback.print_exc()
        return False

def main():
    print("🔍 开始导入测试...")
    
    # 测试基础模块
    test_import("pathlib", "pathlib")
    test_import("configparser", "configparser")
    test_import("requests", "requests")
    
    # 测试我们的模块
    test_import("video_monitor.config.settings", "Settings")
    test_import("video_monitor.utils.ssl_helper", "SSLHelper")
    test_import("video_monitor.api.client", "ApiClient")
    test_import("video_monitor.main", "Main module")
    
    print("🏁 导入测试完成")

if __name__ == "__main__":
    main()
