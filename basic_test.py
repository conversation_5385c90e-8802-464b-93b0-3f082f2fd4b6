#!/usr/bin/env python3
"""
基础测试脚本 - 不依赖SSL模块
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

print("🎯 Python视频监控通道管理器 - 基础测试")
print("=" * 40)

try:
    print("1. 测试加密工具...")
    from video_monitor.utils.crypto import CryptoUtils
    
    result = CryptoUtils.md5("hello")
    print(f"   MD5('hello') = {result}")
    
    if result == "5d41402abc4b2a76b9719d911017c592":
        print("   ✅ 加密工具正常")
    else:
        print("   ❌ 加密工具异常")
    
    print("\n2. 测试数据模型...")
    from video_monitor.models.data_models import ChannelInfo
    
    channel = ChannelInfo(
        channel_id="test_001",
        channel_name="测试通道",
        org_name="测试组织",
        org_id="org_001",
        org_level=1
    )
    
    print(f"   通道信息: {channel}")
    print("   ✅ 数据模型正常")
    
    print("\n3. 测试配置管理...")
    from video_monitor.config.settings import Settings
    
    # 检查资源目录
    resources_dir = Path(__file__).parent / "resources"
    if resources_dir.exists():
        settings = Settings(str(resources_dir))
        print("   ✅ 配置管理正常")
    else:
        print("   ⚠️ resources目录不存在，跳过配置测试")
    
    print("\n4. 测试API端点...")
    from video_monitor.api.endpoints import ApiEndpoints
    
    endpoints = ApiEndpoints("test.example.com", 7282, use_https=True)
    login_url = endpoints.get_login_url()
    print(f"   登录URL: {login_url}")
    print("   ✅ API端点正常")
    
    print("\n🎉 所有基础测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
