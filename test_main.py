#!/usr/bin/env python3
"""
测试主程序运行
"""

import sys
import traceback

def main():
    print("🚀 开始测试主程序...")
    
    try:
        print("导入主模块...")
        from video_monitor.main import RealMonitorChannelManager, main as main_func
        print("✅ 主模块导入成功")
        
        print("创建管理器实例...")
        manager = RealMonitorChannelManager()
        print("✅ 管理器实例创建成功")
        
        print("获取状态信息...")
        status = manager.get_status()
        print(f"✅ 状态信息: {status}")
        
        print("清理资源...")
        manager.logout()
        print("✅ 资源清理完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
        return 1
    
    print("🏁 测试完成")
    return 0

if __name__ == "__main__":
    sys.exit(main())
