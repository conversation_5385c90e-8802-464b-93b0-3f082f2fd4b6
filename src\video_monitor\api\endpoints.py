"""
API端点定义模块

定义所有API端点和相关的URL构建功能。
"""

from typing import Dict, Any, Optional
from urllib.parse import urlencode
import logging

logger = logging.getLogger(__name__)


class ApiEndpoints:
    """API端点管理类"""
    
    # API端点常量（与Java版本保持一致）
    LOGIN_ACTION = "/videoService/accounts/authorize"
    ORG_TREE_ACTION = "/videoService/devicesManager/deviceTree"
    REAL_MONITOR_ACTION = "/videoService/realmonitor/uri"
    KEEP_ALIVE_ACTION = "/videoService/accounts/token/keepalive"
    LOGOUT_ACTION = "/videoService/accounts/unauthorize"
    
    def __init__(self, base_url: str):
        """
        初始化API端点管理器
        
        Args:
            base_url: 基础URL，格式为 https://ip:port
        """
        self.base_url = base_url.rstrip('/')
        logger.debug(f"API endpoints initialized with base URL: {self.base_url}")
    
    def get_login_url(self) -> str:
        """获取登录API的完整URL"""
        return f"{self.base_url}{self.LOGIN_ACTION}"
    
    def get_org_tree_url(self, org_id: str = "", node_type: int = 1, 
                        type_code: str = "01", page: int = 1, 
                        page_size: int = 100) -> str:
        """
        获取组织树API的完整URL
        
        Args:
            org_id: 组织ID，空字符串表示根组织
            node_type: 节点类型，默认为1
            type_code: 类型代码，默认为"01"
            page: 页码，默认为1
            page_size: 页大小，默认为100
            
        Returns:
            完整的URL
        """
        params = {
            'id': org_id,
            'nodeType': node_type,
            'typeCode': type_code,
            'page': page,
            'pageSize': page_size
        }
        
        query_string = urlencode(params)
        url = f"{self.base_url}{self.ORG_TREE_ACTION}?{query_string}"
        
        logger.debug(f"Organization tree URL: {url}")
        return url
    
    def get_org_device_tree_url(self, org_id: str, node_type: int = 1,
                               type_code: str = "01;0;ALL;ALL", page: int = 1,
                               page_size: int = 100) -> str:
        """
        获取组织设备树API的完整URL（用于获取通道信息）
        
        Args:
            org_id: 组织ID
            node_type: 节点类型，默认为1
            type_code: 类型代码，默认为"01;0;ALL;ALL"
            page: 页码，默认为1
            page_size: 页大小，默认为100
            
        Returns:
            完整的URL
        """
        params = {
            'id': org_id,
            'nodeType': node_type,
            'typeCode': type_code,
            'page': page,
            'pageSize': page_size
        }
        
        query_string = urlencode(params)
        url = f"{self.base_url}{self.ORG_TREE_ACTION}?{query_string}"
        
        logger.debug(f"Organization device tree URL: {url}")
        return url
    
    def get_real_monitor_url(self, channel_id: str, scheme: str = "RTSP", 
                           sub_type: int = 0) -> str:
        """
        获取实时监控URI API的完整URL
        
        Args:
            channel_id: 通道ID
            scheme: 协议类型，支持RTSP、FLV_HTTP、HLS，默认为RTSP
            sub_type: 码流类型，0:主码流、1:辅流1、2:辅流2，默认为0
            
        Returns:
            完整的URL
        """
        params = {
            'channelId': channel_id,
            'scheme': scheme,
            'subType': sub_type
        }
        
        query_string = urlencode(params)
        url = f"{self.base_url}{self.REAL_MONITOR_ACTION}?{query_string}"
        
        logger.debug(f"Real monitor URL: {url}")
        return url
    
    def get_keep_alive_url(self) -> str:
        """获取保活API的完整URL"""
        return f"{self.base_url}{self.KEEP_ALIVE_ACTION}"
    
    def get_logout_url(self) -> str:
        """获取登出API的完整URL"""
        return f"{self.base_url}{self.LOGOUT_ACTION}"
    
    def build_url(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> str:
        """
        构建完整的API URL
        
        Args:
            endpoint: API端点路径
            params: 查询参数字典
            
        Returns:
            完整的URL
        """
        url = f"{self.base_url}{endpoint}"
        
        if params:
            # 过滤掉None值
            filtered_params = {k: v for k, v in params.items() if v is not None}
            if filtered_params:
                query_string = urlencode(filtered_params)
                url = f"{url}?{query_string}"
        
        return url
    
    def validate_scheme(self, scheme: str) -> bool:
        """
        验证协议类型是否支持
        
        Args:
            scheme: 协议类型
            
        Returns:
            是否支持
        """
        supported_schemes = {"RTSP", "FLV_HTTP", "HLS"}
        return scheme in supported_schemes
    
    def validate_sub_type(self, sub_type: int) -> bool:
        """
        验证码流类型是否有效
        
        Args:
            sub_type: 码流类型
            
        Returns:
            是否有效
        """
        return sub_type in {0, 1, 2}
    
    def get_all_endpoints(self) -> Dict[str, str]:
        """
        获取所有API端点
        
        Returns:
            端点名称到路径的映射
        """
        return {
            'login': self.LOGIN_ACTION,
            'org_tree': self.ORG_TREE_ACTION,
            'real_monitor': self.REAL_MONITOR_ACTION,
            'keep_alive': self.KEEP_ALIVE_ACTION,
            'logout': self.LOGOUT_ACTION
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"ApiEndpoints(base_url={self.base_url})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        endpoints = self.get_all_endpoints()
        return f"ApiEndpoints(base_url={self.base_url}, endpoints={endpoints})"


# 便捷函数
def create_endpoints(ip: str, port: int, use_https: bool = True) -> ApiEndpoints:
    """
    创建API端点管理器的便捷函数
    
    Args:
        ip: 服务器IP地址
        port: 服务器端口
        use_https: 是否使用HTTPS，默认为True
        
    Returns:
        API端点管理器实例
    """
    protocol = "https" if use_https else "http"
    base_url = f"{protocol}://{ip}:{port}"
    return ApiEndpoints(base_url)


def build_login_data(client_type: str = "winpc", user_name: Optional[str] = None,
                    random_key: Optional[str] = None, signature: Optional[str] = None,
                    encrypt_type: str = "MD5") -> Dict[str, Any]:
    """
    构建登录请求数据
    
    Args:
        client_type: 客户端类型，默认为"winpc"
        user_name: 用户名
        random_key: 随机密钥（第二步登录时需要）
        signature: 签名（第二步登录时需要）
        encrypt_type: 加密类型，默认为"MD5"
        
    Returns:
        登录请求数据字典
    """
    data = {
        "clientType": client_type,
        "userName": user_name
    }
    
    # 第二步登录的额外参数
    if random_key is not None:
        data["randomKey"] = random_key
    if signature is not None:
        data["signature"] = signature
        data["encryptType"] = encrypt_type
    
    return data


def build_keep_alive_data(token: str) -> Dict[str, str]:
    """
    构建保活请求数据
    
    Args:
        token: 认证令牌
        
    Returns:
        保活请求数据字典
    """
    return {"token": token}


def build_logout_data(token: str) -> Dict[str, str]:
    """
    构建登出请求数据
    
    Args:
        token: 认证令牌
        
    Returns:
        登出请求数据字典
    """
    return {"token": token}
