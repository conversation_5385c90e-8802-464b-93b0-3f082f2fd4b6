"""
配置管理模块

提供配置文件读取和管理功能，兼容Java的ResourceBundle格式。
"""

import os
import configparser
from pathlib import Path
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


class Settings:
    """配置管理类，负责读取和管理配置文件"""
    
    def __init__(self, resources_dir: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            resources_dir: 资源文件目录路径，如果为None则自动查找
        """
        self._config_cache: Dict[str, configparser.ConfigParser] = {}
        
        if resources_dir is None:
            # 自动查找resources目录
            current_dir = Path(__file__).parent.parent.parent.parent
            self.resources_dir = current_dir / "resources"
        else:
            self.resources_dir = Path(resources_dir)
            
        if not self.resources_dir.exists():
            raise FileNotFoundError(f"Resources directory not found: {self.resources_dir}")
            
        logger.info(f"Using resources directory: {self.resources_dir}")
    
    def _load_properties_file(self, filename: str) -> configparser.ConfigParser:
        """
        加载properties文件
        
        Args:
            filename: 配置文件名（不含扩展名）
            
        Returns:
            ConfigParser对象
        """
        if filename in self._config_cache:
            return self._config_cache[filename]
            
        file_path = self.resources_dir / f"{filename}.properties"
        
        if not file_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {file_path}")
            
        config = configparser.ConfigParser()
        
        # 读取properties文件，处理没有section的情况
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 如果文件没有section，添加一个默认section
        if not content.strip().startswith('['):
            content = '[DEFAULT]\n' + content
            
        config.read_string(content)
        self._config_cache[filename] = config
        
        logger.debug(f"Loaded configuration file: {file_path}")
        return config
    
    def get_string(self, bundle_name: str, key: str, default: Optional[str] = None) -> str:
        """
        获取字符串配置值
        
        Args:
            bundle_name: 配置文件名（不含扩展名）
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            config = self._load_properties_file(bundle_name)
            value = config.get('DEFAULT', key, fallback=default)
            
            if value is None:
                raise KeyError(f"Configuration key '{key}' not found in '{bundle_name}'")
                
            return value.strip()
            
        except Exception as e:
            logger.error(f"Error reading configuration {bundle_name}.{key}: {e}")
            if default is not None:
                return default
            raise
    
    def set_string(self, bundle_name: str, key: str, value: str) -> None:
        """
        设置字符串配置值并保存到文件
        
        Args:
            bundle_name: 配置文件名（不含扩展名）
            key: 配置键
            value: 配置值
        """
        try:
            config = self._load_properties_file(bundle_name)
            config.set('DEFAULT', key, value)
            
            # 保存到文件
            file_path = self.resources_dir / f"{bundle_name}.properties"
            
            # 写入时去掉[DEFAULT]section标题，保持properties格式
            with open(file_path, 'w', encoding='utf-8') as f:
                for key_name, val in config['DEFAULT'].items():
                    f.write(f"{key_name}={val}\n")
                    
            # 清除缓存，强制重新加载
            if bundle_name in self._config_cache:
                del self._config_cache[bundle_name]
                
            logger.info(f"Updated configuration {bundle_name}.{key} = {value}")
            
        except Exception as e:
            logger.error(f"Error setting configuration {bundle_name}.{key}: {e}")
            raise
    
    def get_int(self, bundle_name: str, key: str, default: Optional[int] = None) -> int:
        """
        获取整数配置值
        
        Args:
            bundle_name: 配置文件名
            key: 配置键
            default: 默认值
            
        Returns:
            整数配置值
        """
        value_str = self.get_string(bundle_name, key, str(default) if default is not None else None)
        try:
            return int(value_str)
        except ValueError:
            if default is not None:
                return default
            raise ValueError(f"Configuration value '{value_str}' is not a valid integer")
    
    def get_bool(self, bundle_name: str, key: str, default: Optional[bool] = None) -> bool:
        """
        获取布尔配置值
        
        Args:
            bundle_name: 配置文件名
            key: 配置键
            default: 默认值
            
        Returns:
            布尔配置值
        """
        value_str = self.get_string(bundle_name, key, str(default).lower() if default is not None else None)
        return value_str.lower() in ('true', '1', 'yes', 'on')
    
    def reload_config(self, bundle_name: str) -> None:
        """
        重新加载指定的配置文件
        
        Args:
            bundle_name: 配置文件名
        """
        if bundle_name in self._config_cache:
            del self._config_cache[bundle_name]
        logger.info(f"Reloaded configuration: {bundle_name}")
    
    def clear_cache(self) -> None:
        """清除所有配置缓存"""
        self._config_cache.clear()
        logger.info("Cleared all configuration cache")


# 全局配置实例
_global_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """获取全局配置实例"""
    global _global_settings
    if _global_settings is None:
        _global_settings = Settings()
    return _global_settings


def init_settings(resources_dir: Optional[str] = None) -> Settings:
    """
    初始化全局配置实例
    
    Args:
        resources_dir: 资源目录路径
        
    Returns:
        配置实例
    """
    global _global_settings
    _global_settings = Settings(resources_dir)
    return _global_settings
