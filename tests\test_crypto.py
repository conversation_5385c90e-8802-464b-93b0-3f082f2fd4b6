"""
加密工具测试模块

测试MD5加密和签名计算功能。
"""

import pytest
from src.video_monitor.utils.crypto import CryptoUtils, md5, calculate_signature


class TestCryptoUtils:
    """加密工具测试类"""
    
    def test_md5_basic(self):
        """测试基本MD5计算"""
        # 测试空字符串
        result = CryptoUtils.md5("")
        assert result == "d41d8cd98f00b204e9800998ecf8427e"
        
        # 测试简单字符串
        result = CryptoUtils.md5("hello")
        assert result == "5d41402abc4b2a76b9719d911017c592"
        
        # 测试中文字符串
        result = CryptoUtils.md5("你好")
        assert len(result) == 32  # MD5结果应该是32位十六进制字符串
        assert result.islower()  # 应该是小写
    
    def test_md5_consistency(self):
        """测试MD5计算的一致性"""
        test_string = "test_consistency"
        result1 = CryptoUtils.md5(test_string)
        result2 = CryptoUtils.md5(test_string)
        assert result1 == result2
    
    def test_calculate_signature(self):
        """测试签名计算"""
        password = "test_password"
        realm = "test_realm"
        random_key = "test_random_key"
        user_name = "test_user"
        
        signature = CryptoUtils.calculate_signature(password, realm, random_key, user_name)
        
        # 验证签名格式
        assert len(signature) == 32  # MD5结果应该是32位
        assert signature.islower()  # 应该是小写
        assert all(c in '0123456789abcdef' for c in signature)  # 应该是十六进制
    
    def test_calculate_signature_consistency(self):
        """测试签名计算的一致性"""
        password = "test_password"
        realm = "test_realm"
        random_key = "test_random_key"
        user_name = "test_user"
        
        signature1 = CryptoUtils.calculate_signature(password, realm, random_key, user_name)
        signature2 = CryptoUtils.calculate_signature(password, realm, random_key, user_name)
        
        assert signature1 == signature2
    
    def test_calculate_signature_different_inputs(self):
        """测试不同输入产生不同签名"""
        base_params = {
            'password': 'test_password',
            'realm': 'test_realm',
            'random_key': 'test_random_key',
            'user_name': 'test_user'
        }
        
        base_signature = CryptoUtils.calculate_signature(**base_params)
        
        # 改变密码
        modified_params = base_params.copy()
        modified_params['password'] = 'different_password'
        different_signature = CryptoUtils.calculate_signature(**modified_params)
        assert base_signature != different_signature
        
        # 改变用户名
        modified_params = base_params.copy()
        modified_params['user_name'] = 'different_user'
        different_signature = CryptoUtils.calculate_signature(**modified_params)
        assert base_signature != different_signature
    
    def test_verify_md5(self):
        """测试MD5验证"""
        test_string = "test_verification"
        expected_hash = CryptoUtils.md5(test_string)
        
        # 正确的哈希值
        assert CryptoUtils.verify_md5(test_string, expected_hash) is True
        
        # 错误的哈希值
        assert CryptoUtils.verify_md5(test_string, "wrong_hash") is False
        
        # 大小写不敏感
        assert CryptoUtils.verify_md5(test_string, expected_hash.upper()) is True
    
    def test_convenience_functions(self):
        """测试便捷函数"""
        test_string = "test_convenience"
        
        # 测试md5便捷函数
        result1 = md5(test_string)
        result2 = CryptoUtils.md5(test_string)
        assert result1 == result2
        
        # 测试calculate_signature便捷函数
        password = "test_password"
        realm = "test_realm"
        random_key = "test_random_key"
        user_name = "test_user"
        
        result1 = calculate_signature(password, realm, random_key, user_name)
        result2 = CryptoUtils.calculate_signature(password, realm, random_key, user_name)
        assert result1 == result2
    
    def test_signature_steps_manual(self):
        """手动验证签名计算步骤"""
        password = "123456"
        realm = "test_realm"
        random_key = "abc123"
        user_name = "testuser"
        
        # 手动计算各步骤
        step1 = CryptoUtils.md5(password)
        step2 = CryptoUtils.md5(user_name + step1)
        step3 = CryptoUtils.md5(step2)
        step4 = CryptoUtils.md5(f"{user_name}:{realm}:{step3}")
        expected_signature = CryptoUtils.md5(f"{step4}:{random_key}")
        
        # 使用函数计算
        actual_signature = CryptoUtils.calculate_signature(password, realm, random_key, user_name)
        
        assert actual_signature == expected_signature
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 空字符串参数
        signature = CryptoUtils.calculate_signature("", "", "", "")
        assert len(signature) == 32
        
        # 包含特殊字符
        signature = CryptoUtils.calculate_signature(
            "p@ssw0rd!",
            "realm:with:colons",
            "key with spaces",
            "<EMAIL>"
        )
        assert len(signature) == 32
        
        # 很长的字符串
        long_string = "a" * 1000
        result = CryptoUtils.md5(long_string)
        assert len(result) == 32


if __name__ == "__main__":
    pytest.main([__file__])
