"""
加密工具模块

提供MD5加密和签名计算功能，与Java版本保持完全一致。
"""

import hashlib
import logging

logger = logging.getLogger(__name__)


class CryptoUtils:
    """加密工具类，提供MD5加密和签名计算功能"""
    
    @staticmethod
    def md5(input_str: str) -> str:
        """
        计算字符串的MD5哈希值
        
        Args:
            input_str: 输入字符串
            
        Returns:
            MD5哈希值（小写十六进制字符串）
        """
        try:
            # 使用UTF-8编码，与Java版本保持一致
            md5_hash = hashlib.md5(input_str.encode('utf-8'))
            hex_string = md5_hash.hexdigest()
            
            logger.debug(f"MD5({input_str[:20]}...) = {hex_string}")
            return hex_string
            
        except Exception as e:
            logger.error(f"MD5 calculation failed: {e}")
            raise
    
    @staticmethod
    def calculate_signature(password: str, realm: str, random_key: str, user_name: str) -> str:
        """
        计算登录签名，与Java版本的calculateSignature方法完全一致
        
        计算步骤：
        1. step1 = MD5(password)
        2. step2 = MD5(userName + step1)
        3. step3 = MD5(step2)
        4. step4 = MD5(userName + ":" + realm + ":" + step3)
        5. signature = MD5(step4 + ":" + randomKey)
        
        Args:
            password: 用户密码
            realm: 认证域
            random_key: 随机密钥
            user_name: 用户名
            
        Returns:
            计算得到的签名字符串
        """
        try:
            logger.debug(f"Calculating signature for user: {user_name}")
            
            # 步骤1: MD5(password)
            step1 = CryptoUtils.md5(password)
            logger.debug(f"Step1 - MD5(password): {step1}")
            
            # 步骤2: MD5(userName + step1)
            step2_input = user_name + step1
            step2 = CryptoUtils.md5(step2_input)
            logger.debug(f"Step2 - MD5(userName + step1): {step2}")
            
            # 步骤3: MD5(step2)
            step3 = CryptoUtils.md5(step2)
            logger.debug(f"Step3 - MD5(step2): {step3}")
            
            # 步骤4: MD5(userName + ":" + realm + ":" + step3)
            step4_input = f"{user_name}:{realm}:{step3}"
            step4 = CryptoUtils.md5(step4_input)
            logger.debug(f"Step4 - MD5(userName:realm:step3): {step4}")
            
            # 步骤5: MD5(step4 + ":" + randomKey)
            signature_input = f"{step4}:{random_key}"
            signature = CryptoUtils.md5(signature_input)
            logger.debug(f"Final signature: {signature}")
            
            return signature
            
        except Exception as e:
            logger.error(f"Signature calculation failed: {e}")
            raise
    
    @staticmethod
    def verify_md5(input_str: str, expected_hash: str) -> bool:
        """
        验证字符串的MD5哈希值
        
        Args:
            input_str: 输入字符串
            expected_hash: 期望的哈希值
            
        Returns:
            验证结果
        """
        try:
            actual_hash = CryptoUtils.md5(input_str)
            result = actual_hash.lower() == expected_hash.lower()
            
            if result:
                logger.debug(f"MD5 verification successful")
            else:
                logger.warning(f"MD5 verification failed: expected {expected_hash}, got {actual_hash}")
                
            return result
            
        except Exception as e:
            logger.error(f"MD5 verification error: {e}")
            return False


# 便捷函数
def md5(input_str: str) -> str:
    """
    计算MD5哈希值的便捷函数
    
    Args:
        input_str: 输入字符串
        
    Returns:
        MD5哈希值
    """
    return CryptoUtils.md5(input_str)


def calculate_signature(password: str, realm: str, random_key: str, user_name: str) -> str:
    """
    计算登录签名的便捷函数
    
    Args:
        password: 用户密码
        realm: 认证域
        random_key: 随机密钥
        user_name: 用户名
        
    Returns:
        签名字符串
    """
    return CryptoUtils.calculate_signature(password, realm, random_key, user_name)
