"""
会话管理模块

实现会话保活机制和优雅登出功能，与Java版本的会话管理保持一致。
"""

import threading
import time
import logging
from typing import Optional, Callable
import atexit

from ..api.client import ApiClient
from ..api.endpoints import ApiEndpoints, build_keep_alive_data, build_logout_data
from ..models.data_models import Constants

logger = logging.getLogger(__name__)


class SessionManager:
    """会话管理器，负责处理会话保活和登出"""
    
    def __init__(self, api_client: ApiClient, endpoints: ApiEndpoints):
        """
        初始化会话管理器
        
        Args:
            api_client: API客户端
            endpoints: API端点管理器
        """
        self.api_client = api_client
        self.endpoints = endpoints
        
        # 会话状态
        self._token: Optional[str] = None
        self._keep_alive_thread: Optional[threading.Thread] = None
        self._stop_keep_alive = threading.Event()
        self._session_active = False
        
        # 回调函数
        self._on_session_expired: Optional[Callable] = None
        
        # 注册退出处理器
        atexit.register(self._cleanup_on_exit)
        
        logger.info("会话管理器初始化完成")
    
    def start_session(self, token: str, on_session_expired: Optional[Callable] = None) -> bool:
        """
        启动会话管理
        
        Args:
            token: 认证令牌
            on_session_expired: 会话过期回调函数
            
        Returns:
            启动是否成功
        """
        try:
            if self._session_active:
                logger.warning("会话已经处于活动状态")
                return True
            
            self._token = token
            self._on_session_expired = on_session_expired
            self._session_active = True
            self._stop_keep_alive.clear()
            
            # 启动保活线程
            self._start_keep_alive_thread()
            
            logger.info("✅ 会话管理已启动，保活线程运行中")
            return True
            
        except Exception as e:
            logger.error(f"启动会话管理失败: {e}")
            return False
    
    def stop_session(self, perform_logout: bool = True) -> bool:
        """
        停止会话管理
        
        Args:
            perform_logout: 是否执行登出操作
            
        Returns:
            停止是否成功
        """
        try:
            if not self._session_active:
                logger.debug("会话未处于活动状态")
                return True
            
            logger.info("🔄 正在停止会话管理...")
            
            # 停止保活线程
            self._stop_keep_alive_thread()
            
            # 执行登出
            logout_success = True
            if perform_logout and self._token:
                logout_success = self._perform_logout()
            
            # 清理状态
            self._cleanup_session_state()
            
            logger.info("✅ 会话管理已停止")
            return logout_success
            
        except Exception as e:
            logger.error(f"停止会话管理失败: {e}")
            return False
    
    def _start_keep_alive_thread(self) -> None:
        """启动保活线程"""
        try:
            self._keep_alive_thread = threading.Thread(
                target=self._keep_alive_worker,
                name="SessionKeepAlive",
                daemon=True
            )
            self._keep_alive_thread.start()
            logger.debug("保活线程已启动")
            
        except Exception as e:
            logger.error(f"启动保活线程失败: {e}")
            raise
    
    def _stop_keep_alive_thread(self) -> None:
        """停止保活线程"""
        try:
            if self._keep_alive_thread and self._keep_alive_thread.is_alive():
                logger.debug("正在停止保活线程...")
                self._stop_keep_alive.set()
                
                # 等待线程结束，最多等待5秒
                self._keep_alive_thread.join(timeout=5.0)
                
                if self._keep_alive_thread.is_alive():
                    logger.warning("保活线程未能在5秒内停止")
                else:
                    logger.debug("保活线程已停止")
                    
        except Exception as e:
            logger.error(f"停止保活线程失败: {e}")
    
    def _keep_alive_worker(self) -> None:
        """保活工作线程"""
        logger.info(f"🔄 会话保活线程启动，间隔: {Constants.KEEP_ALIVE_INTERVAL}秒")
        
        while not self._stop_keep_alive.is_set():
            try:
                # 等待指定间隔时间，或者直到收到停止信号
                if self._stop_keep_alive.wait(timeout=Constants.KEEP_ALIVE_INTERVAL):
                    # 收到停止信号
                    break
                
                # 执行保活请求
                if self._token and self._session_active:
                    success = self._send_keep_alive()
                    if not success:
                        logger.error("保活请求失败，会话可能已过期")
                        self._handle_session_expired()
                        break
                else:
                    logger.warning("Token为空或会话未激活，停止保活")
                    break
                    
            except Exception as e:
                logger.error(f"保活线程异常: {e}")
                # 继续运行，不因为单次异常而停止
                
        logger.info("🛑 会话保活线程已退出")
    
    def _send_keep_alive(self) -> bool:
        """
        发送保活请求
        
        Returns:
            保活是否成功
        """
        try:
            keep_alive_data = build_keep_alive_data(self._token)
            keep_alive_url = self.endpoints.get_keep_alive_url()
            
            response = self.api_client.post(keep_alive_url, data=keep_alive_data, token=self._token)
            
            if response.success:
                logger.debug("💓 会话保活成功")
                return True
            else:
                logger.error(f"会话保活失败: {response.error_message}")
                return False
                
        except Exception as e:
            logger.error(f"发送保活请求异常: {e}")
            return False
    
    def _perform_logout(self) -> bool:
        """
        执行登出操作
        
        Returns:
            登出是否成功
        """
        try:
            logger.info("🚪 正在执行登出...")
            
            logout_data = build_logout_data(self._token)
            logout_url = self.endpoints.get_logout_url()
            
            response = self.api_client.post(logout_url, data=logout_data, token=self._token)
            
            if response.success:
                logger.info("✅ 登出成功")
                return True
            else:
                logger.warning(f"登出失败: {response.error_message}")
                return False
                
        except Exception as e:
            logger.error(f"登出异常: {e}")
            return False
    
    def _handle_session_expired(self) -> None:
        """处理会话过期"""
        try:
            logger.warning("⚠️ 会话已过期")
            
            # 停止保活线程
            self._stop_keep_alive.set()
            
            # 调用回调函数
            if self._on_session_expired:
                try:
                    self._on_session_expired()
                except Exception as e:
                    logger.error(f"会话过期回调函数执行失败: {e}")
            
            # 清理状态
            self._cleanup_session_state()
            
        except Exception as e:
            logger.error(f"处理会话过期异常: {e}")
    
    def _cleanup_session_state(self) -> None:
        """清理会话状态"""
        self._token = None
        self._session_active = False
        self._on_session_expired = None
        self._keep_alive_thread = None
    
    def _cleanup_on_exit(self) -> None:
        """程序退出时的清理操作"""
        try:
            if self._session_active:
                logger.info("程序退出，正在清理会话...")
                self.stop_session(perform_logout=True)
        except Exception as e:
            logger.error(f"程序退出清理异常: {e}")
    
    def is_session_active(self) -> bool:
        """检查会话是否处于活动状态"""
        return self._session_active
    
    def get_token(self) -> Optional[str]:
        """获取当前token"""
        return self._token
    
    def manual_keep_alive(self) -> bool:
        """
        手动执行一次保活
        
        Returns:
            保活是否成功
        """
        if not self._token or not self._session_active:
            logger.warning("会话未激活，无法执行手动保活")
            return False
        
        return self._send_keep_alive()
    
    def get_session_info(self) -> dict:
        """
        获取会话信息
        
        Returns:
            会话信息字典
        """
        return {
            'active': self._session_active,
            'has_token': bool(self._token),
            'keep_alive_running': self._keep_alive_thread is not None and self._keep_alive_thread.is_alive(),
            'keep_alive_interval': Constants.KEEP_ALIVE_INTERVAL
        }
