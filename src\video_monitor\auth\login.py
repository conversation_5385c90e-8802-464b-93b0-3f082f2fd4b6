"""
登录管理模块

实现两步登录流程，与Java版本的登录逻辑保持完全一致。
"""

import json
import logging
from typing import Optional, Tuple

from ..api.client import ApiClient
from ..api.endpoints import ApiEndpoints, build_login_data
from ..utils.crypto import CryptoUtils
from ..models.data_models import LoginResponse, ApiResponse
from ..config.settings import Settings

logger = logging.getLogger(__name__)


class LoginManager:
    """登录管理器，负责处理两步登录流程"""
    
    def __init__(self, api_client: ApiClient, endpoints: ApiEndpoints, settings: Settings):
        """
        初始化登录管理器
        
        Args:
            api_client: API客户端
            endpoints: API端点管理器
            settings: 配置管理器
        """
        self.api_client = api_client
        self.endpoints = endpoints
        self.settings = settings
        
        # 从配置文件读取登录信息
        self.ip = settings.get_string('baseinfo', 'ip')
        self.port = settings.get_int('baseinfo', 'port')
        self.user_name = settings.get_string('baseinfo', 'userName')
        self.password = settings.get_string('baseinfo', 'password')
        
        logger.info(f"登录管理器初始化完成 - 服务器: {self.ip}:{self.port}, 用户: {self.user_name}")
    
    def perform_secure_login(self) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        执行安全SSL证书登录，获取Token
        
        Returns:
            (成功标志, Token, 错误信息)
        """
        try:
            logger.info("🔑 开始安全SSL证书登录...")
            
            # 第一步登录
            first_response = self._first_login_step()
            if not first_response.success:
                return False, None, f"第一步登录失败: {first_response.error_message}"
            
            if not first_response.is_first_step_response():
                return False, None, "第一步登录响应格式错误"
            
            logger.info("✅ 第一步登录成功，获取到认证信息")
            
            # 第二步登录
            second_response = self._second_login_step(first_response.realm, first_response.random_key)
            if not second_response.success:
                return False, None, f"第二步登录失败: {second_response.error_message}"
            
            if not second_response.is_final_response():
                return False, None, "第二步登录响应格式错误，未获取到token"
            
            logger.info("✅ 登录成功，Token已获取")
            
            # 保存token到配置文件
            self._save_token(second_response.token)
            
            return True, second_response.token, None
            
        except Exception as e:
            error_msg = f"登录过程发生异常: {e}"
            logger.error(error_msg)
            return False, None, error_msg
    
    def _first_login_step(self) -> LoginResponse:
        """
        执行第一步登录
        
        Returns:
            登录响应对象
        """
        try:
            # 构建第一步登录数据
            login_data = build_login_data(
                client_type="winpc",
                user_name=self.user_name
            )
            
            # 发送第一步登录请求
            login_url = self.endpoints.get_login_url()
            response = self.api_client.post(login_url, data=login_data)
            
            if not response.success:
                return LoginResponse(
                    success=False,
                    error_message=response.error_message
                )
            
            # 解析响应数据
            data = response.data
            if not data:
                return LoginResponse(
                    success=False,
                    error_message="第一步登录响应为空"
                )
            
            realm = data.get('realm')
            random_key = data.get('randomKey')
            
            if not realm or not random_key:
                return LoginResponse(
                    success=False,
                    error_message="第一步登录响应缺少realm或randomKey"
                )
            
            return LoginResponse(
                success=True,
                realm=realm,
                random_key=random_key
            )
            
        except Exception as e:
            logger.error(f"第一步登录异常: {e}")
            return LoginResponse(
                success=False,
                error_message=f"第一步登录异常: {e}"
            )
    
    def _second_login_step(self, realm: str, random_key: str) -> LoginResponse:
        """
        执行第二步登录
        
        Args:
            realm: 认证域
            random_key: 随机密钥
            
        Returns:
            登录响应对象
        """
        try:
            # 计算签名
            signature = CryptoUtils.calculate_signature(
                password=self.password,
                realm=realm,
                random_key=random_key,
                user_name=self.user_name
            )
            
            # 构建第二步登录数据
            login_data = build_login_data(
                client_type="winpc",
                user_name=self.user_name,
                random_key=random_key,
                signature=signature,
                encrypt_type="MD5"
            )
            
            # 发送第二步登录请求
            login_url = self.endpoints.get_login_url()
            response = self.api_client.post(login_url, data=login_data)
            
            if not response.success:
                return LoginResponse(
                    success=False,
                    error_message=response.error_message
                )
            
            # 解析响应数据
            data = response.data
            if not data:
                return LoginResponse(
                    success=False,
                    error_message="第二步登录响应为空"
                )
            
            token = data.get('token')
            if not token or not token.strip():
                return LoginResponse(
                    success=False,
                    error_message="第二步登录响应中未找到token"
                )
            
            return LoginResponse(
                success=True,
                token=token
            )
            
        except Exception as e:
            logger.error(f"第二步登录异常: {e}")
            return LoginResponse(
                success=False,
                error_message=f"第二步登录异常: {e}"
            )
    
    def _save_token(self, token: str) -> None:
        """
        保存token到配置文件
        
        Args:
            token: 认证令牌
        """
        try:
            self.settings.set_string('token', 'token', token)
            logger.debug("Token已保存到配置文件")
        except Exception as e:
            logger.warning(f"保存Token到配置文件失败: {e}")
    
    def get_saved_token(self) -> Optional[str]:
        """
        从配置文件获取已保存的token
        
        Returns:
            保存的token，如果没有则返回None
        """
        try:
            token = self.settings.get_string('token', 'token', default='')
            if token and token.strip():
                logger.debug("从配置文件读取到已保存的token")
                return token.strip()
            else:
                logger.debug("配置文件中没有有效的token")
                return None
        except Exception as e:
            logger.warning(f"读取保存的token失败: {e}")
            return None
    
    def clear_saved_token(self) -> None:
        """清除保存的token"""
        try:
            self.settings.set_string('token', 'token', '')
            logger.info("已清除保存的token")
        except Exception as e:
            logger.warning(f"清除保存的token失败: {e}")
    
    def validate_credentials(self) -> bool:
        """
        验证登录凭据是否完整
        
        Returns:
            凭据是否有效
        """
        if not self.ip or not self.port:
            logger.error("服务器地址或端口未配置")
            return False
        
        if not self.user_name or not self.password:
            logger.error("用户名或密码未配置")
            return False
        
        return True
    
    def get_login_info(self) -> dict:
        """
        获取登录信息（不包含密码）
        
        Returns:
            登录信息字典
        """
        return {
            'ip': self.ip,
            'port': self.port,
            'user_name': self.user_name,
            'has_password': bool(self.password)
        }
