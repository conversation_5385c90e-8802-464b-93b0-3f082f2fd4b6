"""
集成测试模块

测试整个系统的集成功能，包括登录、会话管理、通道查询等。
"""

import pytest
import tempfile
import time
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from video_monitor.main import RealMonitorChannelManager
from video_monitor.models.data_models import ChannelInfo, MonitorUrlInfo, ApiResponse


class TestIntegration:
    """集成测试类"""
    
    @pytest.fixture
    def mock_resources_dir(self):
        """创建模拟资源目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            resources_dir = Path(temp_dir) / "resources"
            resources_dir.mkdir()
            
            # 创建配置文件
            baseinfo = resources_dir / "baseinfo.properties"
            baseinfo.write_text("""
ip=test.example.com
port=7282
userName=testuser
password=testpass
            """.strip(), encoding='utf-8')
            
            # 创建token文件
            token_file = resources_dir / "token.properties"
            token_file.write_text("", encoding='utf-8')
            
            # 创建模拟证书文件
            (resources_dir / "rootChain.crt").write_text("MOCK_CERT", encoding='utf-8')
            (resources_dir / "key.cer").write_text("MOCK_KEY", encoding='utf-8')
            
            yield str(resources_dir)
    
    @patch('video_monitor.api.client.requests.Session')
    @patch('video_monitor.utils.ssl_helper.ssl.create_default_context')
    def test_successful_login_flow(self, mock_ssl_context, mock_session, mock_resources_dir):
        """测试成功的登录流程"""
        # 模拟SSL上下文
        mock_ssl_context.return_value = Mock()
        
        # 模拟HTTP会话
        mock_session_instance = Mock()
        mock_session.return_value = mock_session_instance
        
        # 模拟登录响应
        login_response1 = Mock()
        login_response1.status_code = 200
        login_response1.json.return_value = {
            'success': True,
            'realm': 'test_realm',
            'randomKey': 'test_random_key'
        }
        
        login_response2 = Mock()
        login_response2.status_code = 200
        login_response2.json.return_value = {
            'success': True,
            'token': 'test_token_12345'
        }
        
        # 模拟保活响应
        keepalive_response = Mock()
        keepalive_response.status_code = 200
        keepalive_response.json.return_value = {'success': True}
        
        # 设置响应序列
        mock_session_instance.post.side_effect = [
            login_response1,  # 第一步登录
            login_response2,  # 第二步登录
            keepalive_response,  # 保活请求
        ]
        
        # 创建管理器并测试登录
        manager = RealMonitorChannelManager(mock_resources_dir)
        
        try:
            # 执行登录
            success = manager.perform_secure_login()
            assert success is True
            
            # 验证状态
            status = manager.get_status()
            assert status['logged_in'] is True
            assert status['has_token'] is True
            
            # 验证调用了正确的API
            assert mock_session_instance.post.call_count >= 2
            
        finally:
            manager.logout()
    
    @patch('video_monitor.api.client.requests.Session')
    @patch('video_monitor.utils.ssl_helper.ssl.create_default_context')
    def test_failed_login_flow(self, mock_ssl_context, mock_session, mock_resources_dir):
        """测试失败的登录流程"""
        # 模拟SSL上下文
        mock_ssl_context.return_value = Mock()
        
        # 模拟HTTP会话
        mock_session_instance = Mock()
        mock_session.return_value = mock_session_instance
        
        # 模拟失败的登录响应
        login_response = Mock()
        login_response.status_code = 401
        login_response.json.return_value = {
            'success': False,
            'message': 'Authentication failed'
        }
        
        mock_session_instance.post.return_value = login_response
        
        # 创建管理器并测试登录
        manager = RealMonitorChannelManager(mock_resources_dir)
        
        try:
            # 执行登录
            success = manager.perform_secure_login()
            assert success is False
            
            # 验证状态
            status = manager.get_status()
            assert status['logged_in'] is False
            assert status['has_token'] is False
            
        finally:
            manager.logout()
    
    @patch('video_monitor.api.client.requests.Session')
    @patch('video_monitor.utils.ssl_helper.ssl.create_default_context')
    def test_channel_retrieval_flow(self, mock_ssl_context, mock_session, mock_resources_dir):
        """测试通道获取流程"""
        # 模拟SSL上下文
        mock_ssl_context.return_value = Mock()
        
        # 模拟HTTP会话
        mock_session_instance = Mock()
        mock_session.return_value = mock_session_instance
        
        # 模拟登录成功
        login_response1 = Mock()
        login_response1.status_code = 200
        login_response1.json.return_value = {
            'success': True,
            'realm': 'test_realm',
            'randomKey': 'test_random_key'
        }
        
        login_response2 = Mock()
        login_response2.status_code = 200
        login_response2.json.return_value = {
            'success': True,
            'token': 'test_token_12345'
        }
        
        # 模拟组织树响应
        org_tree_response = Mock()
        org_tree_response.status_code = 200
        org_tree_response.json.return_value = {
            'success': True,
            'results': [
                {
                    'id': 'org_001',
                    'name': '测试组织1',
                    'nodeType': 1,
                    'typeCode': '01'
                }
            ]
        }
        
        # 模拟设备树响应
        device_tree_response = Mock()
        device_tree_response.status_code = 200
        device_tree_response.json.return_value = {
            'success': True,
            'results': [
                {
                    'id': 'channel_001',
                    'name': '测试通道1',
                    'nodeType': 2,
                    'typeCode': '02'
                },
                {
                    'id': 'channel_002',
                    'name': '测试通道2',
                    'nodeType': 2,
                    'typeCode': '02'
                }
            ]
        }
        
        # 设置GET请求响应
        mock_session_instance.get.side_effect = [
            org_tree_response,    # 组织树查询
            device_tree_response, # 设备树查询
        ]
        
        # 设置POST请求响应
        mock_session_instance.post.side_effect = [
            login_response1,  # 第一步登录
            login_response2,  # 第二步登录
        ]
        
        # 创建管理器并测试
        manager = RealMonitorChannelManager(mock_resources_dir)
        
        try:
            # 登录
            success = manager.perform_secure_login()
            assert success is True
            
            # 获取通道
            channels = manager.get_sub()
            
            # 验证结果
            assert len(channels) == 2
            assert channels[0].channel_id == 'channel_001'
            assert channels[0].channel_name == '测试通道1'
            assert channels[1].channel_id == 'channel_002'
            assert channels[1].channel_name == '测试通道2'
            
        finally:
            manager.logout()
    
    @patch('video_monitor.api.client.requests.Session')
    @patch('video_monitor.utils.ssl_helper.ssl.create_default_context')
    def test_monitor_url_retrieval(self, mock_ssl_context, mock_session, mock_resources_dir):
        """测试监控URL获取"""
        # 模拟SSL上下文
        mock_ssl_context.return_value = Mock()
        
        # 模拟HTTP会话
        mock_session_instance = Mock()
        mock_session.return_value = mock_session_instance
        
        # 模拟登录成功
        login_response1 = Mock()
        login_response1.status_code = 200
        login_response1.json.return_value = {
            'success': True,
            'realm': 'test_realm',
            'randomKey': 'test_random_key'
        }
        
        login_response2 = Mock()
        login_response2.status_code = 200
        login_response2.json.return_value = {
            'success': True,
            'token': 'test_token_12345'
        }
        
        # 模拟监控URL响应
        monitor_url_response = Mock()
        monitor_url_response.status_code = 200
        monitor_url_response.json.return_value = {
            'success': True,
            'url': 'rtsp://test.example.com:554/channel_001'
        }
        
        # 设置响应
        mock_session_instance.post.side_effect = [
            login_response1,
            login_response2,
        ]
        mock_session_instance.get.return_value = monitor_url_response
        
        # 创建管理器并测试
        manager = RealMonitorChannelManager(mock_resources_dir)
        
        try:
            # 登录
            success = manager.perform_secure_login()
            assert success is True
            
            # 获取监控URL
            monitor_url = manager.get_real_monitor_url(
                channel_id='test_channel',
                scheme='RTSP',
                sub_type=0
            )
            
            # 验证结果
            assert monitor_url is not None
            assert monitor_url.channel_id == 'test_channel'
            assert monitor_url.scheme == 'RTSP'
            assert monitor_url.url == 'rtsp://test.example.com:554/channel_001'
            assert monitor_url.sub_type == 0
            
        finally:
            manager.logout()
    
    def test_context_manager(self, mock_resources_dir):
        """测试上下文管理器"""
        with patch('video_monitor.api.client.requests.Session'), \
             patch('video_monitor.utils.ssl_helper.ssl.create_default_context'):
            
            # 测试正常使用
            with RealMonitorChannelManager(mock_resources_dir) as manager:
                assert manager is not None
                status = manager.get_status()
                assert 'logged_in' in status
            
            # 上下文退出后，管理器应该已经登出
            # 这里我们无法直接验证，但确保没有异常抛出
    
    def test_error_recovery(self, mock_resources_dir):
        """测试错误恢复"""
        with patch('video_monitor.api.client.requests.Session') as mock_session, \
             patch('video_monitor.utils.ssl_helper.ssl.create_default_context'):
            
            mock_session_instance = Mock()
            mock_session.return_value = mock_session_instance
            
            # 模拟网络错误
            mock_session_instance.post.side_effect = Exception("Network error")
            
            manager = RealMonitorChannelManager(mock_resources_dir)
            
            try:
                # 登录应该失败但不抛出异常
                success = manager.perform_secure_login()
                assert success is False
                
                # 获取通道应该返回空列表
                channels = manager.get_sub()
                assert channels == []
                
                # 获取监控URL应该返回None
                monitor_url = manager.get_real_monitor_url('test', 'RTSP', 0)
                assert monitor_url is None
                
            finally:
                # 登出应该不抛出异常
                manager.logout()


if __name__ == "__main__":
    pytest.main([__file__])
