# 视频监控通道管理器 - Python版本

这是一个完整的视频监控通道管理系统的Python实现，与原始Java版本功能完全一致。系统提供SSL安全认证、自动登录、会话管理、组织树查询和监控URI获取等功能。

## 🚀 主要功能

- **SSL证书安全认证** - 支持自签名证书和SSL上下文配置
- **两步登录流程** - MD5签名计算，与Java版本算法完全一致
- **自动会话保活** - 110秒间隔自动保活，防止会话过期
- **优雅的会话管理** - 自动登出和资源清理
- **递归组织树查询** - 完整获取组织机构层级结构
- **通道信息收集** - 获取所有视频监控通道详细信息
- **多协议支持** - 支持RTSP、FLV_HTTP、HLS等协议
- **监控URI获取** - 获取实时监控流地址
- **详细日志记录** - 完整的操作日志和错误追踪
- **异常处理** - 程序异常退出时自动清理资源

## 📋 系统要求

- Python 3.8+
- 依赖包：requests, urllib3, cryptography, certifi

## 🛠️ 安装和配置

### 1. 安装依赖

使用uv包管理器（推荐）：

```bash
# 安装uv（如果还没有安装）
pip install uv

# 安装项目依赖
uv sync
```

或使用pip：

```bash
pip install -r requirements.txt
```

### 2. 配置文件

在 `resources/` 目录下配置以下文件：

#### `baseinfo.properties`
```properties
ip=**************
port=7282
userName=haishi
password=haishi@123
```

#### SSL证书文件
- `rootChain.crt` - 根证书链
- `key.cer` - 服务器证书

### 3. 项目结构

```
video_monitor_python/
├── src/video_monitor/           # 主要源代码
│   ├── __init__.py             # 包初始化
│   ├── main.py                 # 主程序入口
│   ├── config/                 # 配置管理
│   │   └── settings.py
│   ├── utils/                  # 工具模块
│   │   ├── crypto.py           # MD5加密工具
│   │   └── ssl_helper.py       # SSL证书处理
│   ├── api/                    # API客户端
│   │   ├── client.py           # HTTP客户端
│   │   └── endpoints.py        # API端点管理
│   ├── auth/                   # 认证模块
│   │   ├── login.py            # 登录管理
│   │   └── session.py          # 会话管理
│   ├── business/               # 业务逻辑
│   │   └── monitor_service.py  # 监控服务
│   └── models/                 # 数据模型
│       └── data_models.py      # 数据结构定义
├── tests/                      # 测试文件
├── resources/                  # 资源文件
│   ├── baseinfo.properties     # 基础配置
│   ├── token.properties        # Token存储
│   ├── rootChain.crt          # 根证书
│   └── key.cer                # 服务器证书
└── pyproject.toml             # 项目配置
```

## 🎯 使用方法

### 基本使用

```python
from video_monitor import RealMonitorChannelManager

# 创建通道管理器
with RealMonitorChannelManager() as manager:
    # 执行登录
    if manager.perform_secure_login():
        print("✅ 登录成功")
        
        # 获取所有通道信息
        channels = manager.get_sub()
        print(f"📡 找到 {len(channels)} 个通道")
        
        # 获取第一个通道的监控URI
        if channels:
            monitor_url = manager.get_real_monitor_url(
                channel_id=channels[0].channel_id,
                scheme="RTSP",
                sub_type=0
            )
            if monitor_url:
                print(f"🎥 监控URI: {monitor_url.url}")
    else:
        print("❌ 登录失败")
```

### 高级使用

```python
from video_monitor import RealMonitorChannelManager

# 创建管理器
manager = RealMonitorChannelManager()

try:
    # 登录
    if not manager.perform_secure_login():
        raise Exception("登录失败")
    
    # 获取所有通道的多种协议URI
    monitor_urls = manager.get_all_monitor_urls(
        schemes=["RTSP", "FLV_HTTP", "HLS"],
        sub_types=[0, 1]  # 主码流和辅流1
    )
    
    # 按协议分组显示
    for scheme in ["RTSP", "FLV_HTTP", "HLS"]:
        scheme_urls = [url for url in monitor_urls if url.scheme == scheme]
        print(f"\n{scheme} 协议 ({len(scheme_urls)} 个):")
        for url in scheme_urls[:3]:  # 只显示前3个
            print(f"  {url.channel_name}: {url.url}")
            
finally:
    # 确保资源清理
    manager.logout()
```

### 命令行运行

```bash
# 运行主程序
python -m video_monitor.main

# 或者直接运行
python src/video_monitor/main.py
```

## 🧪 测试

运行测试套件：

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_crypto.py

# 运行测试并显示覆盖率
pytest --cov=src/video_monitor
```

## 📊 API参考

### 主要类

#### `RealMonitorChannelManager`
主要的通道管理器类。

**方法：**
- `perform_secure_login()` - 执行SSL安全登录
- `get_sub()` - 获取所有通道信息
- `get_real_monitor_url(channel_id, scheme, sub_type)` - 获取监控URI
- `get_all_monitor_urls(schemes, sub_types)` - 批量获取监控URI
- `logout()` - 登出并清理资源
- `get_status()` - 获取系统状态

#### `ChannelInfo`
通道信息数据类。

**属性：**
- `channel_id` - 通道ID
- `channel_name` - 通道名称
- `org_name` - 组织名称
- `org_id` - 组织ID
- `org_level` - 组织级别

#### `MonitorUrlInfo`
监控URL信息数据类。

**属性：**
- `channel_id` - 通道ID
- `channel_name` - 通道名称
- `org_name` - 组织名称
- `scheme` - 协议类型（RTSP/FLV_HTTP/HLS）
- `url` - 监控URL
- `sub_type` - 码流类型（0:主码流，1:辅流1，2:辅流2）

## 🔧 配置说明

### 支持的协议类型
- `RTSP` - 实时流传输协议
- `FLV_HTTP` - HTTP FLV流
- `HLS` - HTTP Live Streaming

### 码流类型
- `0` - 主码流（高清）
- `1` - 辅流1（标清）
- `2` - 辅流2（低清）

### 会话管理
- 自动保活间隔：110秒
- 登录token自动保存和重用
- 程序退出时自动登出

## 🐛 故障排除

### 常见问题

1. **SSL证书错误**
   - 确保 `rootChain.crt` 和 `key.cer` 文件存在
   - 检查证书文件格式（PEM或DER）

2. **登录失败**
   - 验证 `baseinfo.properties` 中的配置
   - 检查网络连接和服务器地址
   - 确认用户名和密码正确

3. **会话过期**
   - 系统会自动重新登录
   - 检查网络稳定性

4. **获取通道失败**
   - 确认用户有相应权限
   - 检查组织树结构

### 日志文件

程序运行时会生成 `video_monitor.log` 日志文件，包含详细的操作记录和错误信息。

## 📝 更新日志

### v1.0.0 (2024-12-19)
- ✅ 完整实现Java版本的所有功能
- ✅ SSL证书安全认证
- ✅ 两步登录流程
- ✅ 自动会话保活
- ✅ 递归组织树查询
- ✅ 多协议监控URI获取
- ✅ 完整的测试套件
- ✅ 详细的文档说明

## 📄 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📞 支持

如有问题，请通过以下方式联系：
- 提交GitHub Issue
- 邮件：<EMAIL>
