"""
数据模型定义

定义系统中使用的数据结构，与Java版本的内部类保持一致。
"""

from dataclasses import dataclass
from typing import Optional, List, Dict, Any
import logging

logger = logging.getLogger(__name__)


@dataclass
class ChannelInfo:
    """
    通道信息类
    
    对应Java版本的ChannelInfo内部类
    """
    channel_id: str
    channel_name: str
    org_name: str
    org_id: str
    org_level: int
    
    def __post_init__(self):
        """数据验证"""
        if not self.channel_id:
            raise ValueError("channel_id cannot be empty")
        if not self.channel_name:
            raise ValueError("channel_name cannot be empty")
        if not self.org_name:
            raise ValueError("org_name cannot be empty")
        if not self.org_id:
            raise ValueError("org_id cannot be empty")
        if self.org_level < 0:
            raise ValueError("org_level must be non-negative")
    
    def __str__(self) -> str:
        """字符串表示，与Java版本的toString方法一致"""
        return f"通道[{self.channel_name}] ID:{self.channel_id} 组织:{self.org_name}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'channel_id': self.channel_id,
            'channel_name': self.channel_name,
            'org_name': self.org_name,
            'org_id': self.org_id,
            'org_level': self.org_level
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChannelInfo':
        """从字典创建实例"""
        return cls(
            channel_id=data['channel_id'],
            channel_name=data['channel_name'],
            org_name=data['org_name'],
            org_id=data['org_id'],
            org_level=data['org_level']
        )


@dataclass
class MonitorUrlInfo:
    """
    监控URL信息类
    
    对应Java版本的MonitorUrlInfo内部类
    """
    channel_id: str
    channel_name: str
    org_name: str
    scheme: str
    url: str
    sub_type: int
    
    def __post_init__(self):
        """数据验证"""
        if not self.channel_id:
            raise ValueError("channel_id cannot be empty")
        if not self.channel_name:
            raise ValueError("channel_name cannot be empty")
        if not self.org_name:
            raise ValueError("org_name cannot be empty")
        if not self.scheme:
            raise ValueError("scheme cannot be empty")
        if not self.url:
            raise ValueError("url cannot be empty")
        if self.sub_type < 0:
            raise ValueError("sub_type must be non-negative")
        
        # 验证支持的协议类型
        supported_schemes = {"RTSP", "FLV_HTTP", "HLS"}
        if self.scheme not in supported_schemes:
            logger.warning(f"Unsupported scheme: {self.scheme}. Supported: {supported_schemes}")
    
    def __str__(self) -> str:
        """字符串表示，与Java版本的toString方法一致"""
        return f"通道[{self.channel_name}] {self.scheme}协议: {self.url}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'channel_id': self.channel_id,
            'channel_name': self.channel_name,
            'org_name': self.org_name,
            'scheme': self.scheme,
            'url': self.url,
            'sub_type': self.sub_type
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MonitorUrlInfo':
        """从字典创建实例"""
        return cls(
            channel_id=data['channel_id'],
            channel_name=data['channel_name'],
            org_name=data['org_name'],
            scheme=data['scheme'],
            url=data['url'],
            sub_type=data['sub_type']
        )
    
    def is_rtsp(self) -> bool:
        """判断是否为RTSP协议"""
        return self.scheme == "RTSP"
    
    def is_flv_http(self) -> bool:
        """判断是否为FLV_HTTP协议"""
        return self.scheme == "FLV_HTTP"
    
    def is_hls(self) -> bool:
        """判断是否为HLS协议"""
        return self.scheme == "HLS"


@dataclass
class OrganizationInfo:
    """
    组织信息类
    
    用于存储组织机构的详细信息
    """
    org_id: str
    org_name: str
    parent_id: Optional[str] = None
    level: int = 0
    node_type: int = 1
    type_code: str = "01"
    children: Optional[List['OrganizationInfo']] = None
    channels: Optional[List[ChannelInfo]] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.children is None:
            self.children = []
        if self.channels is None:
            self.channels = []
    
    def add_child(self, child: 'OrganizationInfo') -> None:
        """添加子组织"""
        if child not in self.children:
            self.children.append(child)
            child.parent_id = self.org_id
            child.level = self.level + 1
    
    def add_channel(self, channel: ChannelInfo) -> None:
        """添加通道"""
        if channel not in self.channels:
            self.channels.append(channel)
    
    def get_total_channels(self) -> int:
        """获取总通道数（包括子组织）"""
        total = len(self.channels)
        for child in self.children:
            total += child.get_total_channels()
        return total
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"组织[{self.org_name}] ID:{self.org_id} 级别:{self.level}"


@dataclass
class LoginResponse:
    """
    登录响应数据类
    """
    success: bool
    token: Optional[str] = None
    realm: Optional[str] = None
    random_key: Optional[str] = None
    error_message: Optional[str] = None
    
    def is_first_step_response(self) -> bool:
        """判断是否为第一步登录响应"""
        return self.realm is not None and self.random_key is not None
    
    def is_final_response(self) -> bool:
        """判断是否为最终登录响应"""
        return self.token is not None


@dataclass
class ApiResponse:
    """
    API响应数据类
    """
    success: bool
    data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    status_code: Optional[int] = None
    
    def get_results(self) -> Optional[List[Dict[str, Any]]]:
        """获取结果列表"""
        if self.data and 'results' in self.data:
            return self.data['results']
        return None
    
    def get_url(self) -> Optional[str]:
        """获取URL（用于监控URI响应）"""
        if self.data and 'url' in self.data:
            return self.data['url']
        return None


# 常量定义
class Constants:
    """系统常量"""
    
    # 支持的协议类型
    SUPPORTED_SCHEMES = ["RTSP", "FLV_HTTP", "HLS"]
    
    # 码流类型
    STREAM_TYPE_MAIN = 0      # 主码流
    STREAM_TYPE_SUB1 = 1      # 辅流1
    STREAM_TYPE_SUB2 = 2      # 辅流2
    
    # 节点类型
    NODE_TYPE_ORG = 1         # 组织节点
    NODE_TYPE_DEVICE = 2      # 设备节点
    NODE_TYPE_CHANNEL = 3     # 通道节点
    
    # API端点
    LOGIN_ACTION = "/videoService/accounts/authorize"
    ORG_TREE_ACTION = "/videoService/devicesManager/deviceTree"
    REAL_MONITOR_ACTION = "/videoService/realmonitor/uri"
    KEEP_ALIVE_ACTION = "/videoService/accounts/token/keepalive"
    LOGOUT_ACTION = "/videoService/accounts/unauthorize"
    
    # 会话管理
    KEEP_ALIVE_INTERVAL = 110  # 保活间隔（秒）
    
    # 分页参数
    DEFAULT_PAGE_SIZE = 100
    DEFAULT_PAGE = 1
