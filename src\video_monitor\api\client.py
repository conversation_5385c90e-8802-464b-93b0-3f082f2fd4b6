"""
API客户端模块

提供HTTPS请求封装，处理SSL证书验证和错误处理。
"""

import json
import ssl
import time
import logging
from typing import Optional, Dict, Any, Union
from urllib.parse import urlparse

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from ..utils.ssl_helper import SSLHelper
from ..models.data_models import ApiResponse

logger = logging.getLogger(__name__)


class ApiClient:
    """API客户端类，提供安全的HTTPS请求功能"""
    
    def __init__(self, ssl_helper: Optional[SSLHelper] = None, 
                 timeout: int = 30, max_retries: int = 3):
        """
        初始化API客户端
        
        Args:
            ssl_helper: SSL助手实例
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
        """
        self.ssl_helper = ssl_helper or SSLHelper()
        self.timeout = timeout
        self.max_retries = max_retries
        
        # 创建会话
        self.session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=max_retries,
            status_forcelist=[429, 500, 502, 503, 504],
            method_whitelist=["HEAD", "GET", "OPTIONS", "POST", "PUT"],
            backoff_factor=1
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'VideoMonitor-Python/1.0.0'
        })
        
        logger.info("API客户端初始化完成")
    
    def _prepare_ssl_context(self) -> None:
        """准备SSL上下文"""
        try:
            ssl_context = self.ssl_helper.create_ssl_context()
            
            # 为requests会话配置SSL
            self.session.verify = False  # 禁用证书验证（因为使用自签名证书）
            
            # 创建自定义的HTTPAdapter来使用我们的SSL上下文
            class SSLAdapter(HTTPAdapter):
                def __init__(self, ssl_context, **kwargs):
                    self.ssl_context = ssl_context
                    super().__init__(**kwargs)
                
                def init_poolmanager(self, *args, **kwargs):
                    kwargs['ssl_context'] = self.ssl_context
                    return super().init_poolmanager(*args, **kwargs)
            
            ssl_adapter = SSLAdapter(ssl_context)
            self.session.mount("https://", ssl_adapter)
            
            logger.debug("SSL上下文配置完成")
            
        except Exception as e:
            logger.warning(f"SSL上下文配置失败，使用默认设置: {e}")
    
    def send_request(self, method: str, url: str, token: Optional[str] = None,
                    data: Optional[Union[Dict[str, Any], str]] = None,
                    params: Optional[Dict[str, Any]] = None) -> ApiResponse:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法（GET, POST, PUT等）
            url: 请求URL
            token: 认证令牌
            data: 请求数据（字典或JSON字符串）
            params: URL参数
            
        Returns:
            API响应对象
        """
        try:
            # 准备SSL上下文
            self._prepare_ssl_context()
            
            # 准备请求头
            headers = {}
            if token:
                headers['X-Subject-Token'] = token
            
            # 准备请求数据
            json_data = None
            if data:
                if isinstance(data, str):
                    json_data = json.loads(data)
                else:
                    json_data = data
            
            logger.debug(f"发送{method}请求到: {url}")
            if json_data:
                logger.debug(f"请求数据: {json.dumps(json_data, ensure_ascii=False)}")
            
            # 发送请求
            start_time = time.time()
            response = self.session.request(
                method=method,
                url=url,
                json=json_data,
                params=params,
                headers=headers,
                timeout=self.timeout
            )
            
            elapsed_time = time.time() - start_time
            logger.debug(f"请求完成，耗时: {elapsed_time:.2f}秒，状态码: {response.status_code}")
            
            # 处理响应
            return self._process_response(response)
            
        except requests.exceptions.Timeout:
            error_msg = f"请求超时: {url}"
            logger.error(error_msg)
            return ApiResponse(success=False, error_message=error_msg)
            
        except requests.exceptions.ConnectionError as e:
            error_msg = f"连接错误: {e}"
            logger.error(error_msg)
            return ApiResponse(success=False, error_message=error_msg)
            
        except requests.exceptions.RequestException as e:
            error_msg = f"请求异常: {e}"
            logger.error(error_msg)
            return ApiResponse(success=False, error_message=error_msg)
            
        except Exception as e:
            error_msg = f"未知错误: {e}"
            logger.error(error_msg)
            return ApiResponse(success=False, error_message=error_msg)
    
    def _process_response(self, response: requests.Response) -> ApiResponse:
        """
        处理HTTP响应
        
        Args:
            response: requests响应对象
            
        Returns:
            API响应对象
        """
        try:
            # 记录响应信息
            logger.debug(f"响应状态码: {response.status_code}")
            logger.debug(f"响应头: {dict(response.headers)}")
            
            # 获取响应文本
            response_text = response.text
            logger.debug(f"响应内容: {response_text[:500]}...")  # 只记录前500个字符
            
            # 检查状态码
            if response.status_code == 200:
                # 成功响应
                try:
                    response_data = response.json()
                    return ApiResponse(
                        success=True,
                        data=response_data,
                        status_code=response.status_code
                    )
                except json.JSONDecodeError:
                    # 如果不是JSON格式，返回原始文本
                    return ApiResponse(
                        success=True,
                        data={'raw_response': response_text},
                        status_code=response.status_code
                    )
                    
            elif response.status_code == 401:
                # 401状态码在第一次登录时是正常的
                try:
                    response_data = response.json()
                    return ApiResponse(
                        success=True,  # 第一次登录的401是正常的
                        data=response_data,
                        status_code=response.status_code
                    )
                except json.JSONDecodeError:
                    return ApiResponse(
                        success=False,
                        error_message=f"认证失败: {response_text}",
                        status_code=response.status_code
                    )
                    
            else:
                # 其他错误状态码
                try:
                    response_data = response.json()
                    error_message = response_data.get('message', f'HTTP {response.status_code}')
                except json.JSONDecodeError:
                    error_message = f"HTTP {response.status_code}: {response_text}"
                
                return ApiResponse(
                    success=False,
                    error_message=error_message,
                    status_code=response.status_code
                )
                
        except Exception as e:
            logger.error(f"处理响应时发生错误: {e}")
            return ApiResponse(
                success=False,
                error_message=f"响应处理错误: {e}",
                status_code=getattr(response, 'status_code', None)
            )
    
    def get(self, url: str, token: Optional[str] = None, 
           params: Optional[Dict[str, Any]] = None) -> ApiResponse:
        """发送GET请求"""
        return self.send_request('GET', url, token=token, params=params)
    
    def post(self, url: str, data: Optional[Union[Dict[str, Any], str]] = None,
            token: Optional[str] = None) -> ApiResponse:
        """发送POST请求"""
        return self.send_request('POST', url, token=token, data=data)
    
    def put(self, url: str, data: Optional[Union[Dict[str, Any], str]] = None,
           token: Optional[str] = None) -> ApiResponse:
        """发送PUT请求"""
        return self.send_request('PUT', url, token=token, data=data)
    
    def delete(self, url: str, token: Optional[str] = None) -> ApiResponse:
        """发送DELETE请求"""
        return self.send_request('DELETE', url, token=token)
    
    def close(self) -> None:
        """关闭会话"""
        if self.session:
            self.session.close()
            logger.debug("API客户端会话已关闭")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()


# 便捷函数
def create_api_client(ssl_helper: Optional[SSLHelper] = None,
                     timeout: int = 30, max_retries: int = 3) -> ApiClient:
    """
    创建API客户端的便捷函数
    
    Args:
        ssl_helper: SSL助手实例
        timeout: 请求超时时间
        max_retries: 最大重试次数
        
    Returns:
        API客户端实例
    """
    return ApiClient(ssl_helper=ssl_helper, timeout=timeout, max_retries=max_retries)
