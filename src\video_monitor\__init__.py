"""
实时监控通道管理器 - Python版本

这是一个完整的视频监控通道管理系统，提供以下功能：
- SSL证书安全认证
- 自动登录获取Token
- 自动会话保活（110秒间隔）
- 优雅的会话登出和清理
- 递归查询完整组织机构树
- 获取所有通道信息
- 支持多种协议 (RTSP, FLV_HTTP, HLS)
- 详细结果报告和统计
- 异常退出时自动清理资源

原始Java版本功能的完整Python实现。
"""

__version__ = "1.0.0"
__author__ = "Video Monitor Team"
__email__ = "<EMAIL>"

# 导出主要类和函数
from .main import RealMonitorChannelManager
from .models.data_models import ChannelInfo, MonitorUrlInfo, OrganizationInfo
from .config.settings import Settings
from .utils.crypto import CryptoUtils
from .utils.ssl_helper import SSLHelper

__all__ = [
    "RealMonitorChannelManager",
    "ChannelInfo",
    "MonitorUrlInfo",
    "OrganizationInfo",
    "Settings",
    "CryptoUtils",
    "SSLHelper",
]
