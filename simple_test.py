#!/usr/bin/env python3
"""
简单测试脚本
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

print("🎯 Python视频监控通道管理器 - 简单测试")
print("=" * 40)

try:
    print("1. 测试加密工具...")
    from video_monitor.utils.crypto import CryptoUtils
    
    result = CryptoUtils.md5("hello")
    print(f"   MD5('hello') = {result}")
    
    if result == "5d41402abc4b2a76b9719d911017c592":
        print("   ✅ 加密工具正常")
    else:
        print("   ❌ 加密工具异常")
    
    print("\n2. 测试数据模型...")
    from video_monitor.models.data_models import ChannelInfo
    
    channel = ChannelInfo(
        channel_id="test_001",
        channel_name="测试通道",
        org_name="测试组织",
        org_id="org_001",
        org_level=1
    )
    
    print(f"   通道信息: {channel}")
    print("   ✅ 数据模型正常")
    
    print("\n3. 测试主类导入...")
    from video_monitor.main import RealMonitorChannelManager
    print("   ✅ 主类导入成功")
    
    print("\n🎉 所有基本测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
