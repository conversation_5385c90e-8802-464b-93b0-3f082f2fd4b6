"""
SSL证书处理模块

提供SSL证书加载和SSL上下文创建功能，与Java版本的SSL处理保持一致。
"""

import ssl
import logging
from pathlib import Path
from typing import Optional

logger = logging.getLogger(__name__)

# 尝试导入cryptography，如果失败则使用基本SSL功能
try:
    from cryptography import x509
    from cryptography.hazmat.backends import default_backend
    HAS_CRYPTOGRAPHY = True
except ImportError:
    logger.warning("cryptography库未安装，将使用基本SSL功能")
    HAS_CRYPTOGRAPHY = False


class SSLHelper:
    """SSL证书处理类"""
    
    def __init__(self, resources_dir: Optional[str] = None):
        """
        初始化SSL助手
        
        Args:
            resources_dir: 资源文件目录路径
        """
        if resources_dir is None:
            # 自动查找resources目录
            current_dir = Path(__file__).parent.parent.parent.parent
            self.resources_dir = current_dir / "resources"
        else:
            self.resources_dir = Path(resources_dir)
            
        if not self.resources_dir.exists():
            raise FileNotFoundError(f"Resources directory not found: {self.resources_dir}")
            
        self._ssl_context: Optional[ssl.SSLContext] = None
        logger.info(f"SSL Helper initialized with resources: {self.resources_dir}")
    
    def _load_certificate(self, cert_filename: str) -> x509.Certificate:
        """
        加载证书文件
        
        Args:
            cert_filename: 证书文件名
            
        Returns:
            证书对象
        """
        cert_path = self.resources_dir / cert_filename
        
        if not cert_path.exists():
            raise FileNotFoundError(f"Certificate file not found: {cert_path}")
            
        try:
            with open(cert_path, 'rb') as cert_file:
                cert_data = cert_file.read()

            if HAS_CRYPTOGRAPHY:
                # 尝试加载PEM格式证书
                try:
                    certificate = x509.load_pem_x509_certificate(cert_data, default_backend())
                    logger.debug(f"Loaded PEM certificate: {cert_filename}")
                except ValueError:
                    # 如果PEM格式失败，尝试DER格式
                    certificate = x509.load_der_x509_certificate(cert_data, default_backend())
                    logger.debug(f"Loaded DER certificate: {cert_filename}")

                return certificate
            else:
                # 没有cryptography库，只返回证书数据
                logger.debug(f"Certificate file loaded (basic mode): {cert_filename}")
                return cert_data

        except Exception as e:
            logger.error(f"Failed to load certificate {cert_filename}: {e}")
            raise
    
    def create_ssl_context(self) -> ssl.SSLContext:
        """
        创建SSL上下文，加载证书文件
        
        Returns:
            配置好的SSL上下文
        """
        if self._ssl_context is not None:
            return self._ssl_context
            
        try:
            logger.info("🔐 正在加载SSL证书...")
            
            # 创建SSL上下文
            context = ssl.create_default_context()
            
            # 允许自签名证书和IP地址连接
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            
            # 加载根证书
            root_cert_path = self.resources_dir / "rootChain.crt"
            if root_cert_path.exists():
                try:
                    context.load_verify_locations(str(root_cert_path))
                    logger.info("✅ 根证书加载成功")
                except Exception as e:
                    logger.warning(f"根证书加载失败，但继续执行: {e}")
            else:
                logger.warning("根证书文件不存在: rootChain.crt")
            
            # 加载服务器证书
            server_cert_path = self.resources_dir / "key.cer"
            if server_cert_path.exists():
                try:
                    # 对于.cer文件，我们只是验证它能被正确加载
                    self._load_certificate("key.cer")
                    logger.info("✅ 服务器证书验证成功")
                except Exception as e:
                    logger.warning(f"服务器证书验证失败，但继续执行: {e}")
            else:
                logger.warning("服务器证书文件不存在: key.cer")
            
            # 设置支持的协议版本
            context.minimum_version = ssl.TLSVersion.TLSv1_2
            context.maximum_version = ssl.TLSVersion.TLSv1_3
            
            # 设置密码套件（可选）
            try:
                context.set_ciphers('ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS')
            except ssl.SSLError:
                # 如果设置密码套件失败，使用默认设置
                logger.debug("使用默认密码套件")
            
            self._ssl_context = context
            logger.info("🔒 SSL上下文初始化完成")
            
            return context
            
        except Exception as e:
            logger.error(f"SSL上下文创建失败: {e}")
            raise
    
    def get_ssl_context(self) -> ssl.SSLContext:
        """
        获取SSL上下文（如果未创建则自动创建）
        
        Returns:
            SSL上下文
        """
        if self._ssl_context is None:
            return self.create_ssl_context()
        return self._ssl_context
    
    def verify_certificate_chain(self) -> bool:
        """
        验证证书链的有效性
        
        Returns:
            验证结果
        """
        try:
            # 加载并验证根证书
            root_cert = self._load_certificate("rootChain.crt")
            logger.debug(f"Root certificate subject: {root_cert.subject}")
            logger.debug(f"Root certificate issuer: {root_cert.issuer}")
            
            # 加载并验证服务器证书
            server_cert = self._load_certificate("key.cer")
            logger.debug(f"Server certificate subject: {server_cert.subject}")
            logger.debug(f"Server certificate issuer: {server_cert.issuer}")
            
            logger.info("证书链验证成功")
            return True
            
        except Exception as e:
            logger.error(f"证书链验证失败: {e}")
            return False
    
    def get_certificate_info(self, cert_filename: str) -> dict:
        """
        获取证书信息
        
        Args:
            cert_filename: 证书文件名
            
        Returns:
            证书信息字典
        """
        try:
            certificate = self._load_certificate(cert_filename)
            
            info = {
                'subject': str(certificate.subject),
                'issuer': str(certificate.issuer),
                'serial_number': str(certificate.serial_number),
                'not_valid_before': certificate.not_valid_before,
                'not_valid_after': certificate.not_valid_after,
                'signature_algorithm': certificate.signature_algorithm_oid._name,
                'version': certificate.version.name,
            }
            
            return info
            
        except Exception as e:
            logger.error(f"获取证书信息失败: {e}")
            raise


# 全局SSL助手实例
_global_ssl_helper: Optional[SSLHelper] = None


def get_ssl_helper() -> SSLHelper:
    """获取全局SSL助手实例"""
    global _global_ssl_helper
    if _global_ssl_helper is None:
        _global_ssl_helper = SSLHelper()
    return _global_ssl_helper


def init_ssl_helper(resources_dir: Optional[str] = None) -> SSLHelper:
    """
    初始化全局SSL助手实例
    
    Args:
        resources_dir: 资源目录路径
        
    Returns:
        SSL助手实例
    """
    global _global_ssl_helper
    _global_ssl_helper = SSLHelper(resources_dir)
    return _global_ssl_helper


def create_ssl_context() -> ssl.SSLContext:
    """
    创建SSL上下文的便捷函数
    
    Returns:
        SSL上下文
    """
    return get_ssl_helper().create_ssl_context()
