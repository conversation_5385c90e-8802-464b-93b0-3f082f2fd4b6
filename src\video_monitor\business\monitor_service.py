"""
监控服务模块

实现组织树递归查询、通道信息收集和监控URI获取功能，与Java版本的业务逻辑保持一致。
"""

import logging
from typing import List, Optional, Dict, Any, Tuple

from ..api.client import ApiClient
from ..api.endpoints import ApiEndpoints
from ..models.data_models import (
    ChannelInfo, MonitorUrlInfo, OrganizationInfo, 
    Constants, ApiResponse
)

logger = logging.getLogger(__name__)


class MonitorService:
    """监控服务类，提供组织树查询和监控URI获取功能"""
    
    def __init__(self, api_client: ApiClient, endpoints: ApiEndpoints, token: str):
        """
        初始化监控服务
        
        Args:
            api_client: API客户端
            endpoints: API端点管理器
            token: 认证令牌
        """
        self.api_client = api_client
        self.endpoints = endpoints
        self.token = token
        
        logger.info("监控服务初始化完成")
    
    def get_sub(self) -> List[ChannelInfo]:
        """
        获取所有通道信息（递归查询组织树）
        
        Returns:
            通道信息列表
        """
        try:
            logger.info("🔍 开始获取所有通道信息...")
            
            # 获取根组织树
            root_org = self._get_organization_tree("")
            if not root_org:
                logger.error("获取根组织树失败")
                return []
            
            # 递归收集所有通道
            all_channels = []
            self._collect_channels_recursive(root_org, all_channels)
            
            logger.info(f"✅ 共获取到 {len(all_channels)} 个通道")
            return all_channels
            
        except Exception as e:
            logger.error(f"获取通道信息异常: {e}")
            return []
    
    def get_org_dev_tree(self, org_id: str = "") -> Optional[OrganizationInfo]:
        """
        获取组织设备树
        
        Args:
            org_id: 组织ID，空字符串表示根组织
            
        Returns:
            组织信息对象
        """
        try:
            logger.debug(f"获取组织设备树: {org_id or '根组织'}")
            
            # 构建请求URL
            url = self.endpoints.get_org_device_tree_url(
                org_id=org_id,
                node_type=1,
                type_code="01;0;ALL;ALL"
            )
            
            # 发送请求
            response = self.api_client.get(url, token=self.token)
            
            if not response.success:
                logger.error(f"获取组织设备树失败: {response.error_message}")
                return None
            
            # 解析响应
            results = response.get_results()
            if not results:
                logger.warning(f"组织 {org_id} 没有子节点")
                return None
            
            # 构建组织信息
            org_info = OrganizationInfo(
                org_id=org_id or "root",
                org_name="根组织" if not org_id else f"组织_{org_id}",
                level=0
            )
            
            # 处理子节点
            for item in results:
                self._process_org_tree_item(item, org_info)
            
            return org_info
            
        except Exception as e:
            logger.error(f"获取组织设备树异常: {e}")
            return None
    
    def _get_organization_tree(self, org_id: str = "") -> Optional[OrganizationInfo]:
        """
        获取组织树（递归查询）
        
        Args:
            org_id: 组织ID
            
        Returns:
            组织信息对象
        """
        try:
            logger.debug(f"查询组织树: {org_id or '根组织'}")
            
            # 构建请求URL
            url = self.endpoints.get_org_tree_url(
                org_id=org_id,
                node_type=1,
                type_code="01"
            )
            
            # 发送请求
            response = self.api_client.get(url, token=self.token)
            
            if not response.success:
                logger.error(f"获取组织树失败: {response.error_message}")
                return None
            
            # 解析响应
            results = response.get_results()
            if not results:
                logger.debug(f"组织 {org_id} 没有子组织")
                return None
            
            # 创建当前组织信息
            current_org = OrganizationInfo(
                org_id=org_id or "root",
                org_name="根组织" if not org_id else f"组织_{org_id}",
                level=0
            )
            
            # 处理子组织
            for item in results:
                child_org = self._create_organization_from_item(item, current_org.level + 1)
                if child_org:
                    current_org.add_child(child_org)
                    
                    # 递归获取子组织的子组织
                    sub_org = self._get_organization_tree(child_org.org_id)
                    if sub_org and sub_org.children:
                        for sub_child in sub_org.children:
                            child_org.add_child(sub_child)
            
            return current_org
            
        except Exception as e:
            logger.error(f"获取组织树异常: {e}")
            return None
    
    def _collect_channels_recursive(self, org: OrganizationInfo, all_channels: List[ChannelInfo]) -> None:
        """
        递归收集组织及其子组织的所有通道
        
        Args:
            org: 组织信息
            all_channels: 通道列表（用于收集结果）
        """
        try:
            # 获取当前组织的设备树（包含通道信息）
            org_dev_tree = self.get_org_dev_tree(org.org_id)
            if org_dev_tree and org_dev_tree.channels:
                all_channels.extend(org_dev_tree.channels)
                logger.debug(f"组织 {org.org_name} 添加了 {len(org_dev_tree.channels)} 个通道")
            
            # 递归处理子组织
            for child_org in org.children:
                self._collect_channels_recursive(child_org, all_channels)
                
        except Exception as e:
            logger.error(f"收集组织 {org.org_name} 的通道时发生异常: {e}")
    
    def _process_org_tree_item(self, item: Dict[str, Any], parent_org: OrganizationInfo) -> None:
        """
        处理组织树项目
        
        Args:
            item: 组织树项目数据
            parent_org: 父组织
        """
        try:
            node_type = item.get('nodeType', 1)
            
            if node_type == Constants.NODE_TYPE_ORG:
                # 组织节点
                child_org = self._create_organization_from_item(item, parent_org.level + 1)
                if child_org:
                    parent_org.add_child(child_org)
                    
            elif node_type == Constants.NODE_TYPE_CHANNEL:
                # 通道节点
                channel = self._create_channel_from_item(item, parent_org)
                if channel:
                    parent_org.add_channel(channel)
                    
        except Exception as e:
            logger.error(f"处理组织树项目异常: {e}")
    
    def _create_organization_from_item(self, item: Dict[str, Any], level: int) -> Optional[OrganizationInfo]:
        """
        从API响应项目创建组织信息
        
        Args:
            item: API响应项目
            level: 组织级别
            
        Returns:
            组织信息对象
        """
        try:
            org_id = item.get('id', '')
            org_name = item.get('name', '')
            
            if not org_id or not org_name:
                logger.warning(f"组织信息不完整: {item}")
                return None
            
            return OrganizationInfo(
                org_id=org_id,
                org_name=org_name,
                level=level,
                node_type=item.get('nodeType', Constants.NODE_TYPE_ORG),
                type_code=item.get('typeCode', '01')
            )
            
        except Exception as e:
            logger.error(f"创建组织信息异常: {e}")
            return None
    
    def _create_channel_from_item(self, item: Dict[str, Any], org: OrganizationInfo) -> Optional[ChannelInfo]:
        """
        从API响应项目创建通道信息
        
        Args:
            item: API响应项目
            org: 所属组织
            
        Returns:
            通道信息对象
        """
        try:
            channel_id = item.get('id', '')
            channel_name = item.get('name', '')
            
            if not channel_id or not channel_name:
                logger.warning(f"通道信息不完整: {item}")
                return None
            
            return ChannelInfo(
                channel_id=channel_id,
                channel_name=channel_name,
                org_name=org.org_name,
                org_id=org.org_id,
                org_level=org.level
            )
            
        except Exception as e:
            logger.error(f"创建通道信息异常: {e}")
            return None
    
    def get_real_monitor_url(self, channel_id: str, scheme: str = "RTSP", 
                           sub_type: int = 0) -> Optional[MonitorUrlInfo]:
        """
        获取实时监控URI
        
        Args:
            channel_id: 通道ID
            scheme: 协议类型（RTSP、FLV_HTTP、HLS）
            sub_type: 码流类型（0:主码流、1:辅流1、2:辅流2）
            
        Returns:
            监控URL信息对象
        """
        try:
            logger.debug(f"获取监控URI: 通道={channel_id}, 协议={scheme}, 码流={sub_type}")
            
            # 验证参数
            if not self.endpoints.validate_scheme(scheme):
                logger.error(f"不支持的协议类型: {scheme}")
                return None
            
            if not self.endpoints.validate_sub_type(sub_type):
                logger.error(f"无效的码流类型: {sub_type}")
                return None
            
            # 构建请求URL
            url = self.endpoints.get_real_monitor_url(
                channel_id=channel_id,
                scheme=scheme,
                sub_type=sub_type
            )
            
            # 发送请求
            response = self.api_client.get(url, token=self.token)
            
            if not response.success:
                logger.error(f"获取监控URI失败: {response.error_message}")
                return None
            
            # 解析响应
            monitor_url = response.get_url()
            if not monitor_url:
                logger.error("响应中没有找到监控URL")
                return None
            
            # 创建监控URL信息对象
            return MonitorUrlInfo(
                channel_id=channel_id,
                channel_name=f"通道_{channel_id}",  # 这里可以从通道信息中获取真实名称
                org_name="未知组织",  # 这里可以从通道信息中获取真实组织名称
                scheme=scheme,
                url=monitor_url,
                sub_type=sub_type
            )
            
        except Exception as e:
            logger.error(f"获取监控URI异常: {e}")
            return None
