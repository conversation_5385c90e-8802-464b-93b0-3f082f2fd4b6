"""
配置管理测试模块

测试配置文件读取和管理功能。
"""

import pytest
import tempfile
import os
from pathlib import Path
from src.video_monitor.config.settings import Settings


class TestSettings:
    """配置管理测试类"""
    
    @pytest.fixture
    def temp_resources_dir(self):
        """创建临时资源目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            resources_dir = Path(temp_dir) / "resources"
            resources_dir.mkdir()
            
            # 创建测试配置文件
            test_config = resources_dir / "test.properties"
            test_config.write_text("""
# 测试配置文件
test_string=hello_world
test_int=42
test_bool=true
test_chinese=你好世界
            """.strip(), encoding='utf-8')
            
            yield str(resources_dir)
    
    def test_init_with_custom_dir(self, temp_resources_dir):
        """测试使用自定义目录初始化"""
        settings = Settings(temp_resources_dir)
        assert settings.resources_dir == Path(temp_resources_dir)
    
    def test_init_with_nonexistent_dir(self):
        """测试使用不存在的目录初始化"""
        with pytest.raises(FileNotFoundError):
            Settings("/nonexistent/directory")
    
    def test_get_string(self, temp_resources_dir):
        """测试获取字符串配置"""
        settings = Settings(temp_resources_dir)
        
        # 正常获取
        value = settings.get_string('test', 'test_string')
        assert value == 'hello_world'
        
        # 获取中文
        value = settings.get_string('test', 'test_chinese')
        assert value == '你好世界'
        
        # 使用默认值
        value = settings.get_string('test', 'nonexistent', 'default_value')
        assert value == 'default_value'
    
    def test_get_string_missing_key(self, temp_resources_dir):
        """测试获取不存在的配置键"""
        settings = Settings(temp_resources_dir)
        
        # 没有默认值应该抛出异常
        with pytest.raises(KeyError):
            settings.get_string('test', 'nonexistent_key')
    
    def test_get_string_missing_file(self, temp_resources_dir):
        """测试获取不存在的配置文件"""
        settings = Settings(temp_resources_dir)
        
        with pytest.raises(FileNotFoundError):
            settings.get_string('nonexistent_file', 'some_key')
    
    def test_get_int(self, temp_resources_dir):
        """测试获取整数配置"""
        settings = Settings(temp_resources_dir)
        
        # 正常获取
        value = settings.get_int('test', 'test_int')
        assert value == 42
        assert isinstance(value, int)
        
        # 使用默认值
        value = settings.get_int('test', 'nonexistent', 100)
        assert value == 100
    
    def test_get_int_invalid_value(self, temp_resources_dir):
        """测试获取无效的整数配置"""
        settings = Settings(temp_resources_dir)
        
        # 字符串不能转换为整数
        with pytest.raises(ValueError):
            settings.get_int('test', 'test_string')
    
    def test_get_bool(self, temp_resources_dir):
        """测试获取布尔配置"""
        settings = Settings(temp_resources_dir)
        
        # 正常获取
        value = settings.get_bool('test', 'test_bool')
        assert value is True
        assert isinstance(value, bool)
        
        # 使用默认值
        value = settings.get_bool('test', 'nonexistent', False)
        assert value is False
    
    def test_set_string(self, temp_resources_dir):
        """测试设置字符串配置"""
        settings = Settings(temp_resources_dir)
        
        # 设置新值
        settings.set_string('test', 'new_key', 'new_value')
        
        # 验证设置成功
        value = settings.get_string('test', 'new_key')
        assert value == 'new_value'
        
        # 修改现有值
        settings.set_string('test', 'test_string', 'modified_value')
        value = settings.get_string('test', 'test_string')
        assert value == 'modified_value'
    
    def test_set_string_chinese(self, temp_resources_dir):
        """测试设置中文配置"""
        settings = Settings(temp_resources_dir)
        
        settings.set_string('test', 'chinese_key', '测试中文值')
        value = settings.get_string('test', 'chinese_key')
        assert value == '测试中文值'
    
    def test_reload_config(self, temp_resources_dir):
        """测试重新加载配置"""
        settings = Settings(temp_resources_dir)
        
        # 首次读取
        value1 = settings.get_string('test', 'test_string')
        
        # 重新加载
        settings.reload_config('test')
        
        # 再次读取
        value2 = settings.get_string('test', 'test_string')
        
        assert value1 == value2
    
    def test_clear_cache(self, temp_resources_dir):
        """测试清除缓存"""
        settings = Settings(temp_resources_dir)
        
        # 读取配置（会缓存）
        settings.get_string('test', 'test_string')
        
        # 清除缓存
        settings.clear_cache()
        
        # 再次读取应该重新加载
        value = settings.get_string('test', 'test_string')
        assert value == 'hello_world'
    
    def test_config_caching(self, temp_resources_dir):
        """测试配置缓存机制"""
        settings = Settings(temp_resources_dir)
        
        # 第一次读取
        value1 = settings.get_string('test', 'test_string')
        
        # 第二次读取（应该使用缓存）
        value2 = settings.get_string('test', 'test_string')
        
        assert value1 == value2
        
        # 验证缓存中有配置
        assert 'test' in settings._config_cache
    
    def test_properties_file_format(self, temp_resources_dir):
        """测试properties文件格式处理"""
        # 创建没有section的properties文件
        no_section_file = Path(temp_resources_dir) / "no_section.properties"
        no_section_file.write_text("""
key1=value1
key2=value2
        """.strip(), encoding='utf-8')
        
        settings = Settings(temp_resources_dir)
        
        # 应该能正常读取
        value = settings.get_string('no_section', 'key1')
        assert value == 'value1'
    
    def test_empty_properties_file(self, temp_resources_dir):
        """测试空的properties文件"""
        empty_file = Path(temp_resources_dir) / "empty.properties"
        empty_file.write_text("", encoding='utf-8')
        
        settings = Settings(temp_resources_dir)
        
        # 读取不存在的键应该使用默认值
        value = settings.get_string('empty', 'any_key', 'default')
        assert value == 'default'
    
    def test_whitespace_handling(self, temp_resources_dir):
        """测试空白字符处理"""
        whitespace_file = Path(temp_resources_dir) / "whitespace.properties"
        whitespace_file.write_text("""
key_with_spaces = value with spaces  
key_with_tabs	=	value with tabs	
        """.strip(), encoding='utf-8')
        
        settings = Settings(temp_resources_dir)
        
        # 应该正确处理空白字符
        value1 = settings.get_string('whitespace', 'key_with_spaces')
        assert value1 == 'value with spaces'
        
        value2 = settings.get_string('whitespace', 'key_with_tabs')
        assert value2 == 'value with tabs'


if __name__ == "__main__":
    pytest.main([__file__])
