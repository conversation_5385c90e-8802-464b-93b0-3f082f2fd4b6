2025-07-03 02:55:19,078 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 02:55:19,078 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 02:55:19,078 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 02:55:19,078 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 02:55:19,079 - __main__ - ERROR - 初始化通道管理器失败: Retry.__init__() got an unexpected keyword argument 'method_whitelist'
2025-07-03 02:55:19,079 - __main__ - ERROR - 程序执行异常: Retry.__init__() got an unexpected keyword argument 'method_whitelist'
2025-07-03 02:55:48,063 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 02:55:48,063 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 02:55:48,063 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 02:55:48,064 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 02:55:48,064 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 02:55:48,065 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: 111.26.219.160:7282, 用户: haishi
2025-07-03 02:55:48,065 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 02:55:48,065 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 02:55:48,065 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 02:55:48,066 - video_monitor.auth.login - INFO - 🔑 开始安全SSL证书登录...
2025-07-03 02:55:48,066 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 02:55:48,106 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 02:55:48,124 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 02:55:48,124 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 02:55:48,378 - video_monitor.auth.login - INFO - ✅ 第一步登录成功，获取到认证信息
2025-07-03 02:55:48,630 - video_monitor.auth.login - INFO - ✅ 登录成功，Token已获取
2025-07-03 02:55:48,630 - video_monitor.config.settings - INFO - Updated configuration token.token = 12563364726704256_b7JmRXztm0hFgoLL4ZpBQhwrGco1HhOf2OMfvCc7fU
2025-07-03 02:55:48,631 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 02:55:48,631 - video_monitor.auth.session - INFO - 🔄 会话保活线程启动，间隔: 110秒
2025-07-03 02:55:48,631 - video_monitor.auth.session - INFO - ✅ 会话管理已启动，保活线程运行中
2025-07-03 02:55:48,632 - __main__ - INFO - ✅ 登录成功，会话管理已启动
2025-07-03 02:55:48,632 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 02:55:48,632 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 02:55:55,879 - video_monitor.business.monitor_service - WARNING - 组织 root 没有子节点
2025-07-03 02:55:59,205 - video_monitor.business.monitor_service - WARNING - 组织 11281471397562560 没有子节点
2025-07-03 02:56:01,706 - video_monitor.business.monitor_service - WARNING - 组织 11281471397824704 没有子节点
2025-07-03 02:56:02,264 - video_monitor.business.monitor_service - WARNING - 组织 11281471397890240 没有子节点
2025-07-03 02:56:02,598 - video_monitor.business.monitor_service - INFO - ✅ 共获取到 85 个通道
2025-07-03 02:56:02,598 - __main__ - INFO - ✅ 成功获取 85 个通道
2025-07-03 02:56:02,598 - __main__ - INFO - 📋 通道列表 (85 个):
2025-07-03 02:56:02,598 - __main__ - INFO -   1. 通道[德惠市大岗渡口（松花江）] ID:22010100581314000134 组织:组织_11281470610868416
2025-07-03 02:56:02,599 - __main__ - INFO -   2. 通道[德惠市大岗渡口全景（松花江）] ID:22010100581314000135 组织:组织_11281470610868416
2025-07-03 02:56:02,599 - __main__ - INFO -   3. 通道[德惠后口子船台（松花江）] ID:22010100581314000136 组织:组织_11281470610868416
2025-07-03 02:56:02,599 - __main__ - INFO -   4. 通道[松原查干湖游船码头] ID:22010100581314000153 组织:组织_11281470610868416
2025-07-03 02:56:02,599 - __main__ - INFO -   5. 通道[松原市滨江公园（松花江）] ID:22010100581314000155 组织:组织_11281470610868416
2025-07-03 02:56:02,600 - __main__ - INFO -   ... 还有 80 个通道
2025-07-03 02:56:02,600 - __main__ - INFO - 🎥 获取监控URI: 22010100581314000134 (RTSP)
2025-07-03 02:56:03,331 - __main__ - INFO - ✅ 成功获取监控URI: rtsp://111.26.219.160:8555/cam/realmonitor?vcuid=22010100581314000134&subtype=0&urlType=agentPull&manufacturer=GBPS&protocoltype=GB28181&streamType=0&mapNet=ExtNet&token=1751482721_32521729e5f39e9c19cfa85f50472e514f639927
2025-07-03 02:56:03,332 - __main__ - INFO - 🎥 示例监控URI: 通道[通道_22010100581314000134] RTSP协议: rtsp://111.26.219.160:8555/cam/realmonitor?vcuid=22010100581314000134&subtype=0&urlType=agentPull&manufacturer=GBPS&protocoltype=GB28181&streamType=0&mapNet=ExtNet&token=1751482721_32521729e5f39e9c19cfa85f50472e514f639927
2025-07-03 02:56:03,332 - __main__ - INFO - ✅ 演示程序执行完成
2025-07-03 02:56:03,332 - __main__ - INFO - 🚪 开始登出...
2025-07-03 02:56:03,333 - video_monitor.auth.session - INFO - 🔄 正在停止会话管理...
2025-07-03 02:56:03,333 - video_monitor.auth.session - INFO - 🛑 会话保活线程已退出
2025-07-03 02:56:03,333 - video_monitor.auth.session - INFO - 🚪 正在执行登出...
2025-07-03 02:56:03,611 - video_monitor.auth.session - INFO - ✅ 登出成功
2025-07-03 02:56:03,611 - video_monitor.auth.session - INFO - ✅ 会话管理已停止
2025-07-03 02:56:03,611 - __main__ - INFO - ✅ 登出完成
2025-07-03 02:56:25,485 - video_monitor.main - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 02:56:25,485 - video_monitor.main - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 02:56:25,486 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 02:56:25,486 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 02:56:25,486 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 02:56:25,487 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: 111.26.219.160:7282, 用户: haishi
2025-07-03 02:56:25,487 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 02:56:25,487 - video_monitor.main - INFO - ✅ 通道管理器初始化完成
2025-07-03 02:56:25,487 - video_monitor.main - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 02:56:25,488 - video_monitor.main - INFO - 发现已保存的token，尝试验证...
2025-07-03 02:56:25,488 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 02:56:25,527 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 02:56:25,531 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 02:56:25,532 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 02:56:25,732 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 02:56:25,732 - video_monitor.main - INFO - ✅ 使用已保存的token登录成功
2025-07-03 02:56:25,732 - video_monitor.main - INFO - 📡 开始获取所有通道信息...
2025-07-03 02:56:25,733 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 02:56:25,939 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 02:56:25,939 - video_monitor.main - INFO - ✅ 成功获取 0 个通道
2025-07-03 02:56:25,939 - video_monitor.main - WARNING - 没有找到任何通道
2025-07-03 02:56:25,939 - video_monitor.main - INFO - 🚪 开始登出...
2025-07-03 02:56:25,939 - video_monitor.main - INFO - ✅ 登出完成
2025-07-03 03:00:21,224 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:00:21,224 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:00:21,225 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:00:21,225 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:00:21,225 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:00:21,226 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: 111.26.219.160:7282, 用户: haishi
2025-07-03 03:00:21,226 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:00:21,227 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:00:21,227 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:00:21,227 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:00:21,227 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:00:21,267 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:00:21,271 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:00:21,272 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:00:21,542 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:00:21,543 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:00:21,543 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:00:21,543 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:00:21,789 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:00:21,790 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:00:21,790 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:00:21,790 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:00:21,790 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:00:47,219 - video_monitor.main - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:00:47,219 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:00:47,220 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:00:47,220 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:00:47,221 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: 111.26.219.160:7282, 用户: haishi
2025-07-03 03:00:47,221 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:00:47,221 - video_monitor.main - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:00:47,222 - video_monitor.main - INFO - 🚪 开始登出...
2025-07-03 03:00:47,222 - video_monitor.main - INFO - ✅ 登出完成
2025-07-03 03:01:50,354 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:01:50,355 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:01:50,355 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:01:50,356 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:01:50,356 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:01:50,357 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: 111.26.219.160:7282, 用户: haishi
2025-07-03 03:01:50,357 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:01:50,357 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:01:50,357 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:01:50,358 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:01:50,358 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:01:50,398 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:01:50,403 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:01:50,403 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:01:50,685 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:01:50,686 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:01:50,686 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:01:50,686 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:01:50,947 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:01:50,948 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:01:50,948 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:01:50,948 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:01:50,948 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:02:16,826 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:02:16,827 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:02:16,827 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:02:16,827 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:02:16,828 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:02:16,828 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: 111.26.219.160:7282, 用户: haishi
2025-07-03 03:02:16,828 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:02:16,829 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:02:16,829 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:02:16,829 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:02:16,829 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:02:16,869 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:02:16,873 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:02:16,873 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:02:17,133 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:02:17,133 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:02:17,133 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:02:17,134 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:02:17,394 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:02:17,394 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:02:17,394 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:02:17,395 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:02:17,395 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:02:35,933 - src.video_monitor.main - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:02:35,933 - src.video_monitor.main - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:02:35,934 - src.video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:02:35,934 - src.video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:02:35,934 - src.video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:02:35,935 - src.video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: 111.26.219.160:7282, 用户: haishi
2025-07-03 03:02:35,935 - src.video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:02:35,935 - src.video_monitor.main - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:02:35,935 - src.video_monitor.main - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:02:35,936 - src.video_monitor.main - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:02:35,936 - src.video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:02:35,976 - src.video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:02:35,980 - src.video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:02:35,980 - src.video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:02:36,216 - src.video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:02:36,216 - src.video_monitor.main - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:02:36,217 - src.video_monitor.main - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:02:36,217 - src.video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:02:36,506 - src.video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:02:36,506 - src.video_monitor.main - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:02:36,507 - src.video_monitor.main - WARNING - 没有找到任何通道
2025-07-03 03:02:36,507 - src.video_monitor.main - INFO - 🚪 开始登出...
2025-07-03 03:02:36,507 - src.video_monitor.main - INFO - ✅ 登出完成
2025-07-03 03:08:53,456 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:08:53,456 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:08:53,456 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:08:53,457 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:08:53,457 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:08:53,458 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: 111.26.219.160:7282, 用户: haishi
2025-07-03 03:08:53,458 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:08:53,458 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:08:53,458 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:08:53,458 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:08:53,459 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:08:53,498 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:08:53,516 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:08:53,516 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:08:53,756 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:08:53,757 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:08:53,757 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:08:53,757 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:08:54,009 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:08:54,010 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:08:54,010 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:08:54,010 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:08:54,011 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:09:23,735 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:09:23,735 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:09:23,735 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:09:23,736 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:09:23,736 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:09:23,737 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: 111.26.219.160:7282, 用户: haishi
2025-07-03 03:09:23,737 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:09:23,737 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:09:23,737 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:09:23,738 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:09:23,738 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:09:23,779 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:09:23,784 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:09:23,785 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:09:24,022 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:09:24,022 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:09:24,023 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:09:24,023 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:09:24,282 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:09:24,282 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:09:24,282 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:09:24,283 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:09:24,283 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:09:28,260 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:09:28,260 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:09:28,261 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:09:28,261 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:09:28,262 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:09:28,262 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: 111.26.219.160:7282, 用户: haishi
2025-07-03 03:09:28,262 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:09:28,263 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:09:28,263 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:09:28,263 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:09:28,263 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:09:28,304 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:09:28,308 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:09:28,309 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:09:28,556 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:09:28,557 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:09:28,557 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:09:28,557 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:09:28,814 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:09:28,814 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:09:28,815 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:09:28,815 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:09:28,815 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:09:46,116 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:09:46,117 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:09:46,117 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:09:46,117 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:09:46,118 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:09:46,118 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: 111.26.219.160:7282, 用户: haishi
2025-07-03 03:09:46,118 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:09:46,119 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:09:46,119 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:09:46,119 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:09:46,119 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:09:46,159 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:09:46,163 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:09:46,163 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:09:46,420 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:09:46,420 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:09:46,421 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:09:46,421 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:09:46,669 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:09:46,670 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:09:46,670 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:09:46,670 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:09:46,670 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:09:48,930 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:09:48,931 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:09:48,931 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:09:48,932 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:09:48,932 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:09:48,933 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: 111.26.219.160:7282, 用户: haishi
2025-07-03 03:09:48,933 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:09:48,933 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:09:48,933 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:09:48,933 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:09:48,934 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:09:48,973 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:09:48,978 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:09:48,978 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:09:49,203 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:09:49,204 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:09:49,204 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:09:49,204 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:09:49,465 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:09:49,466 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:09:49,466 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:09:49,466 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:09:49,466 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:12:18,157 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:12:18,157 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:12:18,158 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:12:18,158 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:12:18,158 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:12:18,159 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: 111.26.219.160:7282, 用户: haishi
2025-07-03 03:12:18,159 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:12:18,159 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:12:18,159 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:12:18,160 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:12:18,160 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:12:18,200 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:12:18,205 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:12:18,205 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:12:18,461 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:12:18,461 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:12:18,461 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:12:18,461 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:12:18,699 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:12:18,699 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:12:18,700 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:12:18,700 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:12:18,700 - __main__ - INFO - ✅ 登出完成
2025-07-03 03:15:06,086 - __main__ - INFO - 🎯 启动视频监控通道管理器演示程序
2025-07-03 03:15:06,086 - __main__ - INFO - 🚀 初始化实时监控通道管理器...
2025-07-03 03:15:06,086 - video_monitor.config.settings - INFO - Using resources directory: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:15:06,087 - video_monitor.utils.ssl_helper - INFO - SSL Helper initialized with resources: D:\workspaces\sszn\sszn_v3.0\video\video_monitor_python\resources
2025-07-03 03:15:06,087 - video_monitor.api.client - INFO - API客户端初始化完成
2025-07-03 03:15:06,088 - video_monitor.auth.login - INFO - 登录管理器初始化完成 - 服务器: 111.26.219.160:7282, 用户: haishi
2025-07-03 03:15:06,088 - video_monitor.auth.session - INFO - 会话管理器初始化完成
2025-07-03 03:15:06,088 - __main__ - INFO - ✅ 通道管理器初始化完成
2025-07-03 03:15:06,088 - __main__ - INFO - 🔐 开始执行安全SSL证书登录...
2025-07-03 03:15:06,089 - __main__ - INFO - 发现已保存的token，尝试验证...
2025-07-03 03:15:06,089 - video_monitor.utils.ssl_helper - INFO - 🔐 正在加载SSL证书...
2025-07-03 03:15:06,128 - video_monitor.utils.ssl_helper - INFO - ✅ 根证书加载成功
2025-07-03 03:15:06,132 - video_monitor.utils.ssl_helper - INFO - ✅ 服务器证书验证成功
2025-07-03 03:15:06,133 - video_monitor.utils.ssl_helper - INFO - 🔒 SSL上下文初始化完成
2025-07-03 03:15:06,346 - video_monitor.business.monitor_service - INFO - 监控服务初始化完成
2025-07-03 03:15:06,347 - __main__ - INFO - ✅ 使用已保存的token登录成功
2025-07-03 03:15:06,347 - __main__ - INFO - 📡 开始获取所有通道信息...
2025-07-03 03:15:06,347 - video_monitor.business.monitor_service - INFO - 🔍 开始获取所有通道信息...
2025-07-03 03:15:06,594 - video_monitor.business.monitor_service - ERROR - 获取根组织树失败
2025-07-03 03:15:06,594 - __main__ - INFO - ✅ 成功获取 0 个通道
2025-07-03 03:15:06,594 - __main__ - WARNING - 没有找到任何通道
2025-07-03 03:15:06,594 - __main__ - INFO - 🚪 开始登出...
2025-07-03 03:15:06,595 - __main__ - INFO - ✅ 登出完成
